"""
Pydantic models for API request/response validation.
"""

from pydantic import BaseModel, Field, validator, field_serializer
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

from ..core.base_model import ParallelConfig, ModelMetrics
from ..comparison.comparator import ComparisonResult


class PrecisionType(str, Enum):
    """Supported precision types for model analysis."""
    FP32 = "fp32"
    FP16 = "fp16"
    BF16 = "bf16"
    FP8 = "fp8"
    INT8 = "int8"
    INT4 = "int4"


class ParallelConfigModel(BaseModel):
    """Pydantic model for parallel configuration."""
    tensor_parallel_size: int = Field(default=1, ge=1, description="Tensor parallel size")
    pipeline_parallel_size: int = Field(default=1, ge=1, description="Pipeline parallel size")
    data_parallel_size: int = Field(default=1, ge=1, description="Data parallel size")
    expert_parallel_size: int = Field(default=1, ge=1, description="Expert parallel size (for MoE models)")
    expert_data_parallel_size: int = Field(default=1, ge=1, description="Expert data parallel size (for MoE models)")
    
    def to_parallel_config(self) -> ParallelConfig:
        """Convert to core ParallelConfig object."""
        return ParallelConfig(
            tensor_parallel_size=self.tensor_parallel_size,
            pipeline_parallel_size=self.pipeline_parallel_size,
            data_parallel_size=self.data_parallel_size,
            expert_parallel_size=self.expert_parallel_size,
            expert_data_parallel_size=self.expert_data_parallel_size
        )
    
    @classmethod
    def from_parallel_config(cls, config: ParallelConfig) -> 'ParallelConfigModel':
        """Create from core ParallelConfig object."""
        return cls(
            tensor_parallel_size=config.tensor_parallel_size,
            pipeline_parallel_size=config.pipeline_parallel_size,
            data_parallel_size=config.data_parallel_size,
            expert_parallel_size=config.expert_parallel_size,
            expert_data_parallel_size=config.expert_data_parallel_size
        )


class ModelAnalysisRequest(BaseModel):
    """Request model for model analysis endpoint."""
    model_names: List[str] = Field(
        ..., 
        min_items=1, 
        description="List of model names to analyze"
    )
    sequence_length: int = Field(
        default=2048, 
        ge=1, 
        le=32768, 
        description="Input sequence length"
    )
    batch_size: int = Field(
        default=1, 
        ge=1, 
        le=1024, 
        description="Batch size for analysis"
    )
    parallel_config: Optional[ParallelConfigModel] = Field(
        default=None,
        description="Default parallel configuration for all models"
    )
    parallel_configs: Optional[List[ParallelConfigModel]] = Field(
        default=None,
        description="Individual parallel configurations for each model"
    )
    precision: PrecisionType = Field(
        default=PrecisionType.FP16,
        description="Model precision for memory calculations (backward compatibility)"
    )
    # Mixed precision support
    weight_dtype: Optional[str] = Field(
        default=None,
        description="Data type for model weights (fp32, fp16, bf16, int8, fp8, fp4)"
    )
    activation_dtype: Optional[str] = Field(
        default=None,
        description="Data type for activations (fp32, fp16, bf16, int8, fp8)"
    )
    grad_dtype: Optional[str] = Field(
        default=None,
        description="Data type for gradients (fp32, fp16, bf16)"
    )
    optimizer_dtype: Optional[str] = Field(
        default=None,
        description="Data type for optimizer states (fp32, fp16, bf16)"
    )
    kv_cache_dtype: Optional[str] = Field(
        default=None,
        description="Data type for KV cache (fp32, fp16, bf16, int8, fp8)"
    )
    training: bool = Field(
        default=False,
        description="Whether to calculate memory for training (includes gradients/optimizer)"
    )
    include_shapes: bool = Field(
        default=True,
        description="Whether to include matrix shape analysis"
    )
    include_comparison: bool = Field(
        default=True,
        description="Whether to include model comparison results"
    )
    
    @validator('parallel_configs')
    def validate_parallel_configs(cls, v, values):
        """Validate that parallel_configs length matches model_names length."""
        if v is not None and 'model_names' in values:
            if len(v) != len(values['model_names']):
                raise ValueError("Number of parallel configs must match number of models")
        return v
    
    @validator('model_names')
    def validate_model_names(cls, v):
        """Validate model names are not empty."""
        if not v:
            raise ValueError("At least one model name must be provided")
        for name in v:
            if not name.strip():
                raise ValueError("Model names cannot be empty")
        return v
    
    @validator('weight_dtype', 'activation_dtype', 'grad_dtype', 'optimizer_dtype', 'kv_cache_dtype')
    def validate_dtype(cls, v):
        """Validate dtype is supported."""
        if v is None:
            return v
        supported_dtypes = ['fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4']
        if v not in supported_dtypes:
            raise ValueError(f"Unsupported dtype: {v}. Supported types: {supported_dtypes}")
        return v


class ModelMetricsModel(BaseModel):
    """Pydantic model for model metrics."""
    model_name: str
    architecture: str
    total_params: int
    attention_params: int
    mlp_params: int
    embedding_params: int
    flops_forward: int
    flops_per_token: int
    memory_params: int
    memory_activations: int
    memory_total: int
    attention_shapes: Dict[str, List[int]] = Field(default_factory=dict)
    mlp_shapes: Dict[str, List[int]] = Field(default_factory=dict)
    parallel_config: Optional[ParallelConfigModel] = None
    sequence_length: int
    batch_size: int
    timestamp: datetime
    experts_per_token: Optional[int] = None
    active_params_per_token: Optional[int] = None
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()
    
    @classmethod
    def from_model_metrics(cls, metrics: ModelMetrics) -> 'ModelMetricsModel':
        """Create from core ModelMetrics object."""
        # Convert tuple shapes to lists for JSON serialization
        attention_shapes = {
            k: list(v) if isinstance(v, tuple) else v 
            for k, v in metrics.attention_shapes.items()
        }
        mlp_shapes = {
            k: list(v) if isinstance(v, tuple) else v 
            for k, v in metrics.mlp_shapes.items()
        }
        
        return cls(
            model_name=metrics.model_name,
            architecture=metrics.architecture,
            total_params=metrics.total_params,
            attention_params=metrics.attention_params,
            mlp_params=metrics.mlp_params,
            embedding_params=metrics.embedding_params,
            flops_forward=metrics.flops_forward,
            flops_per_token=metrics.flops_per_token,
            memory_params=metrics.memory_params,
            memory_activations=metrics.memory_activations,
            memory_total=metrics.memory_total,
            attention_shapes=attention_shapes,
            mlp_shapes=mlp_shapes,
            parallel_config=ParallelConfigModel.from_parallel_config(metrics.parallel_config) if metrics.parallel_config else None,
            sequence_length=metrics.sequence_length,
            batch_size=metrics.batch_size,
            timestamp=metrics.timestamp,
            experts_per_token=metrics.experts_per_token,
            active_params_per_token=metrics.active_params_per_token
        )


class ComparisonResultModel(BaseModel):
    """Pydantic model for comparison results."""
    models: List[str]
    metrics: Dict[str, List[Any]]
    parallel_configs: List[ParallelConfigModel]
    sequence_length: int
    batch_size: int
    timestamp: datetime
    metadata: Dict[str, Any] = Field(default_factory=dict)
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()
    
    @classmethod
    def from_comparison_result(cls, result: ComparisonResult) -> 'ComparisonResultModel':
        """Create from core ComparisonResult object."""
        return cls(
            models=result.models,
            metrics=result.metrics,
            parallel_configs=[
                ParallelConfigModel.from_parallel_config(config) 
                for config in result.parallel_configs
            ],
            sequence_length=result.sequence_length,
            batch_size=result.batch_size,
            timestamp=result.timestamp,
            metadata=result.metadata
        )


class ModelAnalysisResponse(BaseModel):
    """Response model for model analysis endpoint."""
    results: Dict[str, ModelMetricsModel] = Field(
        description="Individual model analysis results"
    )
    comparison: Optional[ComparisonResultModel] = Field(
        default=None,
        description="Model comparison results"
    )
    execution_time: float = Field(
        description="Analysis execution time in seconds"
    )
    timestamp: datetime = Field(
        description="Analysis completion timestamp"
    )
    request_id: str = Field(
        description="Unique identifier for this analysis request"
    )
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()
    
    @validator('results')
    def validate_results(cls, v):
        """Validate that results are not empty."""
        if not v:
            raise ValueError("Analysis results cannot be empty")
        return v


class ArchitectureInfo(BaseModel):
    """Information about a model architecture."""
    examples: List[str] = Field(description="Example model names for this architecture")
    features: List[str] = Field(description="Key features of this architecture")
    supports_moe: bool = Field(description="Whether this architecture supports Mixture of Experts")


class SupportedModelsResponse(BaseModel):
    """Response model for supported models endpoint."""
    architectures: List[str] = Field(description="List of supported architecture names")
    architecture_info: Dict[str, ArchitectureInfo] = Field(
        description="Detailed information about each architecture"
    )
    total_architectures: int = Field(description="Total number of supported architectures")


class AnalysisStatusEnum(str, Enum):
    """Status values for analysis tasks."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class AnalysisStatus(BaseModel):
    """Status information for analysis tasks."""
    request_id: str
    status: AnalysisStatusEnum
    progress: float = Field(ge=0.0, le=1.0, description="Progress percentage (0.0 to 1.0)")
    message: str = Field(description="Status message")
    created_at: datetime
    updated_at: datetime
    completed_at: Optional[datetime] = None
    error: Optional[str] = None
    result: Optional[ModelAnalysisResponse] = None


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(description="Error message")
    status_code: int = Field(description="HTTP status code")
    timestamp: datetime = Field(description="Error timestamp")
    details: Optional[str] = Field(default=None, description="Additional error details")
    request_id: Optional[str] = Field(default=None, description="Request ID if available")
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()


class ValidationErrorDetail(BaseModel):
    """Detailed validation error information."""
    field: str = Field(description="Field that failed validation")
    message: str = Field(description="Validation error message")
    value: Any = Field(description="Invalid value")


class ValidationErrorResponse(BaseModel):
    """Response model for validation errors."""
    error: str = Field(description="General error message")
    status_code: int = Field(description="HTTP status code")
    timestamp: datetime = Field(description="Error timestamp")
    validation_errors: List[ValidationErrorDetail] = Field(
        description="Detailed validation error information"
    )
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str = Field(description="Health status")
    timestamp: datetime = Field(description="Health check timestamp")
    version: str = Field(description="API version")
    uptime: Optional[float] = Field(default=None, description="Uptime in seconds")
    dependencies: Optional[Dict[str, str]] = Field(
        default=None, 
        description="Status of external dependencies"
    )
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()


class ModelValidationResponse(BaseModel):
    """Response model for model validation endpoint."""
    valid: bool = Field(description="Whether the model is valid and supported")
    model_name: str = Field(description="Name of the validated model")
    architecture: Optional[str] = Field(default=None, description="Detected architecture")
    supported: bool = Field(description="Whether the architecture is supported")
    config_available: bool = Field(description="Whether model configuration is available")
    basic_info: Optional[Dict[str, Any]] = Field(
        default=None, 
        description="Basic model information if available"
    )
    error: Optional[str] = Field(default=None, description="Error message if validation failed")
    error_type: Optional[str] = Field(default=None, description="Type of error encountered")


# Custom JSON encoders for complex types
# Memory Analysis Models

class MemoryAnalysisRequest(BaseModel):
    """Request model for memory analysis endpoint."""
    model_names: List[str] = Field(
        ..., 
        min_items=1, 
        description="List of model names to analyze"
    )
    sequence_length: int = Field(
        default=2048, 
        ge=1, 
        le=32768, 
        description="Input sequence length"
    )
    batch_size: int = Field(
        default=1, 
        ge=1, 
        le=1024, 
        description="Batch size for analysis"
    )
    dtype: str = Field(
        default='fp16',
        description="Default data type for calculations (backward compatibility)"
    )
    weight_dtype: Optional[str] = Field(
        default=None,
        description="Data type for model weights (fp32, fp16, bf16, int8, fp8, fp4)"
    )
    activation_dtype: Optional[str] = Field(
        default=None,
        description="Data type for activations (fp32, fp16, bf16, int8, fp8)"
    )
    grad_dtype: Optional[str] = Field(
        default=None,
        description="Data type for gradients (fp32, fp16, bf16)"
    )
    optimizer_dtype: Optional[str] = Field(
        default=None,
        description="Data type for optimizer states (fp32, fp16, bf16)"
    )
    kv_cache_dtype: Optional[str] = Field(
        default=None,
        description="Data type for KV cache (fp32, fp16, bf16, int8, fp8)"
    )
    training: bool = Field(
        default=False,
        description="Whether to calculate memory for training (includes gradients/optimizer)"
    )
    include_total_memory: bool = Field(
        default=False,
        description="Whether to include total memory calculations"
    )
    include_kv_cache: bool = Field(
        default=True,
        description="Whether to include KV cache memory calculations"
    )
    
    @validator('dtype', 'weight_dtype', 'activation_dtype', 'grad_dtype', 'optimizer_dtype', 'kv_cache_dtype')
    def validate_dtype(cls, v):
        """Validate dtype is supported."""
        if v is None:
            return v
        supported_dtypes = ['fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4']
        if v not in supported_dtypes:
            raise ValueError(f"Unsupported dtype: {v}. Supported types: {supported_dtypes}")
        return v
    
    @validator('model_names')
    def validate_model_names(cls, v):
        """Validate model names are not empty."""
        if not v:
            raise ValueError("At least one model name must be provided")
        for name in v:
            if not name.strip():
                raise ValueError("Model names cannot be empty")
        return v


class KVGrowthAnalysisRequest(BaseModel):
    """Request model for KV cache growth analysis endpoint."""
    model_names: List[str] = Field(
        ..., 
        min_items=1, 
        description="List of model names to analyze"
    )
    min_sequence_length: int = Field(
        default=512, 
        ge=1, 
        le=32768, 
        description="Minimum sequence length for analysis"
    )
    max_sequence_length: int = Field(
        default=32768, 
        ge=1, 
        le=65536, 
        description="Maximum sequence length for analysis"
    )
    sequence_length_step: int = Field(
        default=1024, 
        ge=1, 
        le=8192, 
        description="Step size for sequence length increments"
    )
    batch_size: int = Field(
        default=1, 
        ge=1, 
        le=1024, 
        description="Batch size for analysis"
    )
    dtype: str = Field(
        default='fp16',
        description="Default data type for calculations (backward compatibility)"
    )
    kv_cache_dtype: Optional[str] = Field(
        default=None,
        description="Data type for KV cache memory calculations (fp32, fp16, bf16, int8, fp8)"
    )
    
    @validator('dtype', 'kv_cache_dtype')
    def validate_dtype(cls, v):
        """Validate dtype is supported."""
        if v is None:
            return v
        supported_dtypes = ['fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4']
        if v not in supported_dtypes:
            raise ValueError(f"Unsupported dtype: {v}. Supported types: {supported_dtypes}")
        return v
    
    @validator('max_sequence_length')
    def validate_sequence_length_range(cls, v, values):
        """Validate max_sequence_length is greater than min_sequence_length."""
        if 'min_sequence_length' in values and v <= values['min_sequence_length']:
            raise ValueError("max_sequence_length must be greater than min_sequence_length")
        return v
    
    @validator('model_names')
    def validate_model_names(cls, v):
        """Validate model names are not empty."""
        if not v:
            raise ValueError("At least one model name must be provided")
        for name in v:
            if not name.strip():
                raise ValueError("Model names cannot be empty")
        return v


class MemoryBreakdown(BaseModel):
    """Memory breakdown information for a model."""
    parameters: int = Field(description="Memory used by model parameters in bytes")
    kv_cache: int = Field(default=0, description="Memory used by KV cache in bytes")
    activations: int = Field(description="Memory used by activations in bytes")
    gradients: Optional[int] = Field(default=None, description="Memory used by gradients in bytes (training only)")
    optimizer_states: Optional[int] = Field(default=None, description="Memory used by optimizer states in bytes (training only)")
    total: int = Field(description="Total memory usage in bytes")
    dtype: str = Field(description="Default data type used for calculations")
    dtypes: Optional[Dict[str, Optional[str]]] = Field(
        default=None, 
        description="Mixed precision data types for different components"
    )
    attention_mechanism: str = Field(description="Attention mechanism type (MHA, GQA, MLA)")
    training: bool = Field(default=False, description="Whether this breakdown is for training")
    
    @validator('attention_mechanism')
    def validate_attention_mechanism(cls, v):
        """Validate attention mechanism type."""
        valid_mechanisms = ['MHA', 'GQA', 'MLA', 'Unknown']
        if v not in valid_mechanisms:
            raise ValueError(f"Invalid attention mechanism: {v}. Valid types: {valid_mechanisms}")
        return v


class KVGrowthPoint(BaseModel):
    """Data point for KV cache memory growth analysis."""
    sequence_length: int = Field(description="Sequence length for this data point")
    memory_bytes: int = Field(description="KV cache memory usage in bytes")
    memory_human: str = Field(description="Human-readable memory usage (e.g., '1.2 GB')")
    
    @validator('sequence_length')
    def validate_sequence_length(cls, v):
        """Validate sequence length is positive."""
        if v <= 0:
            raise ValueError("Sequence length must be positive")
        return v
    
    @validator('memory_bytes')
    def validate_memory_bytes(cls, v):
        """Validate memory bytes is non-negative."""
        if v < 0:
            raise ValueError("Memory bytes must be non-negative")
        return v


class MemoryAnalysisResponse(BaseModel):
    """Response model for memory analysis endpoint."""
    model_results: Dict[str, MemoryBreakdown] = Field(
        description="Memory breakdown results for each model"
    )
    kv_growth_data: Optional[Dict[str, List[KVGrowthPoint]]] = Field(
        default=None,
        description="KV cache growth data for each model (if requested)"
    )
    execution_time: float = Field(
        description="Analysis execution time in seconds"
    )
    timestamp: datetime = Field(
        description="Analysis completion timestamp"
    )
    request_id: str = Field(
        description="Unique identifier for this analysis request"
    )
    
    @field_serializer('timestamp')
    def serialize_timestamp(self, value: datetime) -> str:
        return value.isoformat()
    
    @validator('model_results')
    def validate_model_results(cls, v):
        """Validate that model results are not empty."""
        if not v:
            raise ValueError("Model results cannot be empty")
        return v
    
    @validator('execution_time')
    def validate_execution_time(cls, v):
        """Validate execution time is non-negative."""
        if v < 0:
            raise ValueError("Execution time must be non-negative")
        return v


class CustomJSONEncoder:
    """Custom JSON encoder for complex types."""
    
    @staticmethod
    def encode_datetime(obj: datetime) -> str:
        """Encode datetime objects."""
        return obj.isoformat()
    
    @staticmethod
    def encode_model_metrics(obj: ModelMetrics) -> Dict[str, Any]:
        """Encode ModelMetrics objects."""
        return ModelMetricsModel.from_model_metrics(obj).dict()
    
    @staticmethod
    def encode_comparison_result(obj: ComparisonResult) -> Dict[str, Any]:
        """Encode ComparisonResult objects."""
        return ComparisonResultModel.from_comparison_result(obj).dict()