"""
FastAPI web application for LLM modeling metrics analysis.
"""

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks, WebSocket, WebSocketDisconnect, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, FileResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
import asyncio
import logging
from datetime import datetime, timedelta
import traceback
import json
import time
import os
from collections import defaultdict, deque

from ..core.base_model import ParallelConfig, ModelMetrics
from ..core.model_factory import ModelFactory, ModelNotSupportedError, ConfigurationError
from ..comparison.comparator import Comparator, ComparisonResult
from .models import (
    ModelAnalysisRequest, 
    ModelAnalysisResponse, 
    SupportedModelsResponse,
    ErrorResponse,
    AnalysisStatus,
    ModelMetricsModel,
    ComparisonResultModel,
    MemoryAnalysisRequest,
    MemoryAnalysisResponse,
    KVGrowthAnalysisRequest,
    MemoryBreakdown,
    KVGrowthPoint
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="LLM Modeling Metrics API",
    description="API for analyzing computational requirements and performance characteristics of Large Language Models",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
import os
static_dir = os.path.join(os.path.dirname(__file__), "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Initialize components and register models
from ..models.dense_model import DenseModel
from ..models.moe_model import MoEModel

model_factory = ModelFactory()

# Register model architectures
# model_factory.register_model('llama', DenseModel)
# model_factory.register_model('qwen', DenseModel)
# model_factory.register_model('deepseek', MoEModel)

model_factory.register_model('moe', MoEModel)  # Generic MoE fallback

comparator = Comparator()

# Rate limiting function
def check_rate_limit(client_ip: str) -> bool:
    """
    Check if client has exceeded rate limit.
    
    Args:
        client_ip: Client IP address
        
    Returns:
        True if within rate limit, False if exceeded
    """
    now = time.time()
    client_requests = rate_limit_storage[client_ip]
    
    # Remove old requests outside the window
    while client_requests and client_requests[0] < now - RATE_LIMIT_WINDOW:
        client_requests.popleft()
    
    # Check if limit exceeded
    if len(client_requests) >= RATE_LIMIT_REQUESTS:
        return False
    
    # Add current request
    client_requests.append(now)
    return True

def get_client_ip(request: Request) -> str:
    """Get client IP address from request."""
    # Check for forwarded headers (when behind proxy)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # Fallback to direct connection
    return request.client.host if request.client else "unknown"

async def rate_limit_dependency(request: Request):
    """Dependency for rate limiting."""
    client_ip = get_client_ip(request)
    
    if not check_rate_limit(client_ip):
        raise HTTPException(
            status_code=429,
            detail=f"Rate limit exceeded. Maximum {RATE_LIMIT_REQUESTS} requests per {RATE_LIMIT_WINDOW} seconds.",
            headers={"Retry-After": str(RATE_LIMIT_WINDOW)}
        )
    
    return client_ip

# In-memory storage for analysis status (use Redis/database in production)
analysis_status: Dict[str, AnalysisStatus] = {}

# Rate limiting storage (use Redis in production)
rate_limit_storage: Dict[str, deque] = defaultdict(lambda: deque())

# WebSocket connection manager
class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept a WebSocket connection."""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"WebSocket client {client_id} connected")
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection."""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"WebSocket client {client_id} disconnected")
    
    async def send_personal_message(self, message: dict, client_id: str):
        """Send a message to a specific client."""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)
    
    async def broadcast(self, message: dict):
        """Broadcast a message to all connected clients."""
        disconnected_clients = []
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)

manager = ConnectionManager()

# Rate limiting configuration
RATE_LIMIT_REQUESTS = int(os.getenv("RATE_LIMIT_REQUESTS", "10"))  # requests per window
RATE_LIMIT_WINDOW = int(os.getenv("RATE_LIMIT_WINDOW", "60"))      # window in seconds

# API token configuration
API_TOKEN = os.getenv("API_TOKEN")  # Optional API token
security = HTTPBearer(auto_error=False) if API_TOKEN else None

def verify_token_dependency():
    """Create token verification dependency based on configuration."""
    if API_TOKEN is None:
        # No authentication required
        async def no_auth():
            return True
        return no_auth
    else:
        # Authentication required
        async def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
            if credentials.credentials != API_TOKEN:
                raise HTTPException(
                    status_code=401,
                    detail="Invalid API token",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return True
        return verify_token

# Create the dependency
verify_token = verify_token_dependency()

@app.get("/")
async def root():
    """Serve the main dashboard HTML file."""
    static_dir = os.path.join(os.path.dirname(__file__), "static")
    index_path = os.path.join(static_dir, "index.html")
    
    if os.path.exists(index_path):
        return FileResponse(index_path)
    else:
        # Fallback to API information if static files not available
        return {
            "name": "LLM Modeling Metrics API",
            "version": "1.0.0",
            "description": "API for analyzing computational requirements of Large Language Models",
            "docs": "/docs",
            "health": "/health"
        }

@app.get("/api", response_model=Dict[str, str])
async def api_root():
    """API root endpoint with information."""
    return {
        "name": "LLM Modeling Metrics API",
        "version": "1.0.0",
        "description": "API for analyzing computational requirements of Large Language Models",
        "docs": "/docs",
        "health": "/health"
    }


@app.get("/health", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }


@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """
    WebSocket endpoint for real-time analysis progress updates.
    
    Clients can connect to receive real-time updates about analysis progress,
    including status changes, progress percentages, and completion notifications.
    
    Args:
        websocket: WebSocket connection
        client_id: Unique identifier for the client
    """
    await manager.connect(websocket, client_id)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle ping/pong for connection health
            if message.get("type") == "ping":
                await manager.send_personal_message(
                    {"type": "pong", "timestamp": datetime.now().isoformat()},
                    client_id
                )
            
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        manager.disconnect(client_id)

@app.post("/api/analyze", response_model=ModelAnalysisResponse)
async def analyze_models(
    request: ModelAnalysisRequest,
    background_tasks: BackgroundTasks,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
) -> ModelAnalysisResponse:
    """
    Analyze multiple models and return comprehensive metrics.
    
    This endpoint performs detailed analysis of the specified models including:
    - Parameter counts (total, attention, MLP, embedding)
    - FLOP calculations for forward pass
    - Memory requirements (parameters and activations)
    - Matrix shapes under different parallel configurations
    - Efficiency metrics and comparisons
    
    Args:
        request: Model analysis request with model names and configuration
        background_tasks: FastAPI background tasks for async processing
        
    Returns:
        ModelAnalysisResponse with detailed metrics and comparison results
        
    Raises:
        HTTPException: If analysis fails or models are not supported
    """
    try:
        start_time = datetime.now()
        
        # Validate request
        if not request.model_names:
            raise HTTPException(
                status_code=400,
                detail="At least one model name must be provided"
            )
        
        # Prepare parallel configurations
        parallel_configs = []
        if request.parallel_configs:
            if len(request.parallel_configs) != len(request.model_names):
                raise HTTPException(
                    status_code=400,
                    detail="Number of parallel configs must match number of models"
                )
            parallel_configs = [config.to_parallel_config() for config in request.parallel_configs]
        else:
            # Use default parallel config for all models
            default_config = request.parallel_config.to_parallel_config() if request.parallel_config else ParallelConfig()
            parallel_configs = [default_config] * len(request.model_names)
        
        # Create request ID and initialize status
        request_id = f"analysis_{int(start_time.timestamp())}_{hash(str(request.model_names))}"
        
        # Initialize analysis status
        analysis_status[request_id] = AnalysisStatus(
            request_id=request_id,
            status="running",
            progress=0.0,
            message="Starting analysis...",
            created_at=start_time,
            updated_at=start_time
        )
        
        # Notify WebSocket clients about analysis start
        await manager.broadcast({
            "type": "analysis_started",
            "request_id": request_id,
            "models": request.model_names,
            "timestamp": start_time.isoformat()
        })
        
        # Perform model comparison with progress updates
        logger.info(f"Starting analysis for models: {request.model_names}")
        
        try:
            # Update progress: Starting comparison
            analysis_status[request_id].progress = 0.1
            analysis_status[request_id].message = "Fetching model configurations..."
            analysis_status[request_id].updated_at = datetime.now()
            
            await manager.broadcast({
                "type": "progress_update",
                "request_id": request_id,
                "progress": 0.1,
                "message": "Fetching model configurations..."
            })
            
            comparison_result = comparator.compare_models(
                model_names=request.model_names,
                sequence_length=request.sequence_length,
                batch_size=request.batch_size,
                parallel_configs=parallel_configs,
                precision=request.precision
            )
            
            # Update progress: Comparison completed
            analysis_status[request_id].progress = 0.8
            analysis_status[request_id].message = "Generating individual metrics..."
            analysis_status[request_id].updated_at = datetime.now()
            
            await manager.broadcast({
                "type": "progress_update",
                "request_id": request_id,
                "progress": 0.8,
                "message": "Generating individual metrics..."
            })
            
        except Exception as e:
            # Update status on error
            analysis_status[request_id].status = "failed"
            analysis_status[request_id].error = str(e)
            analysis_status[request_id].updated_at = datetime.now()
            
            await manager.broadcast({
                "type": "analysis_failed",
                "request_id": request_id,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            
            raise
        
        # Generate individual model metrics
        individual_results = {}
        for i, model_name in enumerate(request.model_names):
            try:
                model = model_factory.create_model(model_name)
                metrics = model.get_metrics(
                    sequence_length=request.sequence_length,
                    batch_size=request.batch_size,
                    parallel_config=parallel_configs[i]
                )
                
                # If mixed precision is specified, recalculate memory requirements
                if any([request.weight_dtype, request.activation_dtype, request.grad_dtype, 
                       request.optimizer_dtype, request.kv_cache_dtype]):
                    memory_breakdown = model.compute_memory_requirements(
                        sequence_length=request.sequence_length,
                        batch_size=request.batch_size,
                        dtype=request.precision.value,
                        weight_dtype=request.weight_dtype,
                        activation_dtype=request.activation_dtype,
                        grad_dtype=request.grad_dtype,
                        optimizer_dtype=request.optimizer_dtype,
                        kv_cache_dtype=request.kv_cache_dtype,
                        training=request.training,
                        include_kv_cache=True
                    )
                    
                    # Update metrics with mixed precision memory calculations
                    metrics.memory_params = memory_breakdown.get('parameters', 0)
                    metrics.memory_activations = memory_breakdown.get('activations', 0)
                    metrics.memory_total = memory_breakdown.get('total', 0)
                
                individual_results[model_name] = ModelMetricsModel.from_model_metrics(metrics)
            except Exception as e:
                logger.error(f"Error getting metrics for {model_name}: {e}")
                # Create placeholder metrics for failed models
                placeholder_metrics = ModelMetrics(
                    model_name=model_name,
                    architecture="unknown",
                    total_params=0,
                    attention_params=0,
                    mlp_params=0,
                    embedding_params=0,
                    flops_forward=0,
                    flops_per_token=0,
                    memory_params=0,
                    memory_activations=0,
                    memory_total=0,
                    sequence_length=request.sequence_length,
                    batch_size=request.batch_size,
                    parallel_config=parallel_configs[i]
                )
                individual_results[model_name] = ModelMetricsModel.from_model_metrics(placeholder_metrics)
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        # Create response
        response = ModelAnalysisResponse(
            results=individual_results,
            comparison=ComparisonResultModel.from_comparison_result(comparison_result) if request.include_comparison else None,
            execution_time=execution_time,
            timestamp=end_time,
            request_id=request_id
        )
        
        # Update final status
        analysis_status[request_id].status = "completed"
        analysis_status[request_id].progress = 1.0
        analysis_status[request_id].message = "Analysis completed successfully"
        analysis_status[request_id].completed_at = end_time
        analysis_status[request_id].updated_at = end_time
        analysis_status[request_id].result = response
        
        # Notify WebSocket clients about completion
        await manager.broadcast({
            "type": "analysis_completed",
            "request_id": request_id,
            "execution_time": execution_time,
            "timestamp": end_time.isoformat(),
            "models_analyzed": len(individual_results)
        })
        
        logger.info(f"Analysis completed in {execution_time:.2f} seconds")
        
        return response
        
    except ModelNotSupportedError as e:
        logger.error(f"Model not supported: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Model not supported: {str(e)}"
        )
    except ConfigurationError as e:
        logger.error(f"Configuration error: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Configuration error: {str(e)}"
        )
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error during analysis: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@app.get("/api/models/supported", response_model=SupportedModelsResponse)
async def get_supported_models(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
) -> SupportedModelsResponse:
    """
    Get list of supported model architectures and example models.
    
    Returns information about:
    - Supported model architectures (llama, deepseek, qwen, etc.)
    - Example model names for each architecture
    - Architecture-specific features and capabilities
    
    Note: All model names are supported by defaulting to appropriate architectures.
    
    Returns:
        SupportedModelsResponse with architecture information
    """
    try:
        # Get supported architectures from model factory
        architectures = model_factory.get_supported_architectures()
        
        # Define example models and features for each architecture
        architecture_info = {
            "dense": {
                "examples": [
                    # "meta-llama/Llama-2-7b-hf",
                    # "meta-llama/Llama-2-13b-hf",
                    "meta-llama/Meta-Llama-3-8B-Instruct",
                    "meta-llama/Meta-Llama-3-70B-Instruct",
                     "Qwen/Qwen3-8B",
                    "Qwen/Qwen3-32B",
                ],
                "features": [
                    "Dense transformer architecture",
                    "Grouped Query Attention (GQA)",
                    "RMSNorm normalization",
                    "SwiGLU activation",
                    "Default fallback architecture"
                ],
                "supports_moe": False
            },
            "moe": {
                "examples": [
                    "deepseek-ai/DeepSeek-V3",
                    "moonshotai/Kimi-K2-Instruct",
                    "openai/gpt-oss-120b",
                    "openai/gpt-oss-20b",
                    "Qwen/Qwen3-235B-A22B",
                    "Qwen/Qwen3-Coder-480B-A35B-Instruct",
                ],
                "features": [
                    "Generic Mixture of Experts architecture",
                    "Fallback for models with MoE indicators"
                ],
                "supports_moe": True
            }
        }
        
        # Build response with registered architectures
        supported_info = {}
        for arch in architectures:
            if arch in architecture_info:
                supported_info[arch] = architecture_info[arch]
            else:
                # Handle any custom registered architectures
                supported_info[arch] = {
                    "examples": [f"Custom {arch} models"],
                    "features": ["Custom architecture"],
                    "supports_moe": False
                }
        
        return SupportedModelsResponse(
            architectures=list(supported_info.keys()),
            architecture_info=supported_info,
            total_architectures=len(supported_info)
        )
        
    except Exception as e:
        logger.error(f"Error getting supported models: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving supported models: {str(e)}"
        )


@app.get("/api/models/validate/{model_name}")
async def validate_model(
    model_name: str,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
) -> Dict[str, Any]:
    """
    Validate if a model is supported and can be analyzed.
    
    Args:
        model_name: Name of the model to validate
        
    Returns:
        Dictionary with validation results and model information
    """
    try:
        # Try to create model instance to validate
        model = model_factory.create_model(model_name)
        
        # Get basic model information
        config = model.config
        architecture = getattr(config, 'model_type', 'unknown')
        
        return {
            "valid": True,
            "model_name": model_name,
            "architecture": architecture,
            "supported": True,
            "config_available": config is not None,
            "basic_info": {
                "hidden_size": getattr(config, 'hidden_size', None),
                "num_layers": getattr(config, 'num_hidden_layers', None),
                "num_heads": getattr(config, 'num_attention_heads', None),
                "vocab_size": getattr(config, 'vocab_size', None)
            }
        }
        
    except ModelNotSupportedError as e:
        return {
            "valid": False,
            "model_name": model_name,
            "supported": False,
            "error": str(e),
            "error_type": "unsupported_architecture"
        }
    except ConfigurationError as e:
        return {
            "valid": False,
            "model_name": model_name,
            "supported": True,
            "config_available": False,
            "error": str(e),
            "error_type": "configuration_error"
        }
    except Exception as e:
        return {
            "valid": False,
            "model_name": model_name,
            "error": str(e),
            "error_type": "unknown_error"
        }


@app.get("/api/analysis/status/{request_id}")
async def get_analysis_status(
    request_id: str,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
) -> AnalysisStatus:
    """
    Get the status of a background analysis task.
    
    Args:
        request_id: ID of the analysis request
        
    Returns:
        AnalysisStatus with current status information
    """
    if request_id not in analysis_status:
        raise HTTPException(
            status_code=404,
            detail=f"Analysis request {request_id} not found"
        )
    
    return analysis_status[request_id]


@app.get("/api/analysis/list")
async def list_analysis_requests(
    limit: int = 50,
    status_filter: Optional[str] = None,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
) -> Dict[str, Any]:
    """
    List recent analysis requests with optional filtering.
    
    Args:
        limit: Maximum number of requests to return
        status_filter: Filter by status (pending, running, completed, failed)
        
    Returns:
        Dictionary with analysis request list and metadata
    """
    # Filter and sort analysis requests
    filtered_requests = []
    
    for request_id, status in analysis_status.items():
        if status_filter and status.status != status_filter:
            continue
        filtered_requests.append(status)
    
    # Sort by creation time (newest first)
    filtered_requests.sort(key=lambda x: x.created_at, reverse=True)
    
    # Apply limit
    limited_requests = filtered_requests[:limit]
    
    return {
        "requests": limited_requests,
        "total_count": len(filtered_requests),
        "returned_count": len(limited_requests),
        "status_counts": {
            "pending": sum(1 for s in analysis_status.values() if s.status == "pending"),
            "running": sum(1 for s in analysis_status.values() if s.status == "running"),
            "completed": sum(1 for s in analysis_status.values() if s.status == "completed"),
            "failed": sum(1 for s in analysis_status.values() if s.status == "failed")
        }
    }


@app.delete("/api/analysis/{request_id}")
async def cancel_analysis(
    request_id: str,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
) -> Dict[str, str]:
    """
    Cancel a running analysis request.
    
    Args:
        request_id: ID of the analysis request to cancel
        
    Returns:
        Cancellation status message
    """
    if request_id not in analysis_status:
        raise HTTPException(
            status_code=404,
            detail=f"Analysis request {request_id} not found"
        )
    
    status = analysis_status[request_id]
    
    if status.status in ["completed", "failed", "cancelled"]:
        raise HTTPException(
            status_code=400,
            detail=f"Cannot cancel analysis in status: {status.status}"
        )
    
    # Update status to cancelled
    status.status = "cancelled"
    status.updated_at = datetime.now()
    status.error = "Analysis cancelled by user"
    
    # Notify WebSocket clients
    await manager.broadcast({
        "type": "analysis_cancelled",
        "request_id": request_id,
        "timestamp": datetime.now().isoformat()
    })
    
    return {"message": f"Analysis request {request_id} cancelled successfully"}





@app.get("/api/stats")
async def get_api_stats(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
) -> Dict[str, Any]:
    """
    Get API usage statistics and system information.
    
    Returns:
        Dictionary with API statistics and system info
    """
    # Calculate uptime
    import psutil
    import sys
    
    current_time = datetime.now()
    
    # Analysis statistics
    total_analyses = len(analysis_status)
    completed_analyses = sum(1 for s in analysis_status.values() if s.status == "completed")
    failed_analyses = sum(1 for s in analysis_status.values() if s.status == "failed")
    
    # Recent activity (last 24 hours)
    recent_cutoff = current_time - timedelta(hours=24)
    recent_analyses = sum(
        1 for s in analysis_status.values() 
        if s.created_at >= recent_cutoff
    )
    
    # System information
    memory_info = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)
    
    return {
        "api_info": {
            "version": "1.0.0",
            "uptime_seconds": time.time() - psutil.Process().create_time(),
            "current_time": current_time.isoformat()
        },
        "analysis_stats": {
            "total_analyses": total_analyses,
            "completed_analyses": completed_analyses,
            "failed_analyses": failed_analyses,
            "success_rate": completed_analyses / max(total_analyses, 1) * 100 if total_analyses > 0 else 0,
            "recent_24h": recent_analyses
        },
        "system_info": {
            "python_version": sys.version,
            "memory_usage_percent": memory_info.percent,
            "memory_available_gb": memory_info.available / (1024**3),
            "cpu_usage_percent": cpu_percent
        },
        "websocket_info": {
            "active_connections": len(manager.active_connections),
            "connection_ids": list(manager.active_connections.keys())
        },
        "rate_limiting": {
            "requests_per_window": RATE_LIMIT_REQUESTS,
            "window_seconds": RATE_LIMIT_WINDOW,
            "active_clients": len(rate_limit_storage)
        }
    }


@app.post("/api/export")
async def export_results(
    request: Dict[str, Any],
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
):
    """
    Export analysis results in various formats.
    
    Args:
        request: Export request containing comparison data and format
        
    Returns:
        File response with exported data
    """
    try:
        comparison_data = request.get('comparison')
        export_format = request.get('format', 'json')
        request_id = request.get('request_id')
        
        if not comparison_data:
            raise HTTPException(
                status_code=400,
                detail="Comparison data is required for export"
            )
        
        # Create comparison result object
        comparison = ComparisonResult(
            models=comparison_data['models'],
            metrics=comparison_data['metrics'],
            parallel_configs=[],  # Will be populated if needed
            sequence_length=comparison_data.get('sequence_length', 2048),
            batch_size=comparison_data.get('batch_size', 1),
            timestamp=datetime.now(),
            metadata=comparison_data.get('metadata', {})
        )
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if export_format == 'json':
            # Export as JSON
            import tempfile
            import json
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
                json.dump(comparison_data, f, indent=2, default=str)
                temp_path = f.name
            
            return FileResponse(
                temp_path,
                media_type='application/json',
                filename=f'model_comparison_{timestamp}.json',
                headers={"Content-Disposition": f"attachment; filename=model_comparison_{timestamp}.json"}
            )
            
        elif export_format == 'csv':
            # Export as CSV
            import tempfile
            import pandas as pd
            
            # Convert metrics to DataFrame
            df_data = []
            for metric_name, values in comparison_data['metrics'].items():
                for i, model in enumerate(comparison_data['models']):
                    if i < len(values):
                        df_data.append({
                            'Model': model,
                            'Metric': metric_name,
                            'Value': values[i]
                        })
            
            df = pd.DataFrame(df_data)
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                df.to_csv(f.name, index=False)
                temp_path = f.name
            
            return FileResponse(
                temp_path,
                media_type='text/csv',
                filename=f'model_comparison_{timestamp}.csv',
                headers={"Content-Disposition": f"attachment; filename=model_comparison_{timestamp}.csv"}
            )
            
        elif export_format == 'excel':
            # Export as Excel
            import tempfile
            import pandas as pd
            
            with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
                temp_path = f.name
            
            # Create Excel writer
            with pd.ExcelWriter(temp_path, engine='openpyxl') as writer:
                # Summary sheet
                summary_data = []
                for i, model in enumerate(comparison_data['models']):
                    row = {'Model': model}
                    for metric_name, values in comparison_data['metrics'].items():
                        if i < len(values):
                            row[metric_name] = values[i]
                    summary_data.append(row)
                
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='Summary', index=False)
                
                # Individual metric sheets
                for metric_name, values in comparison_data['metrics'].items():
                    metric_df = pd.DataFrame({
                        'Model': comparison_data['models'][:len(values)],
                        metric_name: values
                    })
                    # Clean sheet name for Excel
                    sheet_name = metric_name.replace('_', ' ').title()[:31]
                    metric_df.to_excel(writer, sheet_name=sheet_name, index=False)
            
            return FileResponse(
                temp_path,
                media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                filename=f'model_comparison_{timestamp}.xlsx',
                headers={"Content-Disposition": f"attachment; filename=model_comparison_{timestamp}.xlsx"}
            )
            
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported export format: {export_format}"
            )
            
    except Exception as e:
        logger.error(f"Export failed: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Export failed: {str(e)}"
        )


# Memory Analysis Endpoints

@app.post("/api/memory/analyze", response_model=MemoryAnalysisResponse)
async def analyze_memory_requirements(
    request: MemoryAnalysisRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
) -> MemoryAnalysisResponse:
    """
    Analyze memory requirements with dtype and sequence length variations.
    
    This endpoint provides detailed memory analysis including:
    - Parameter memory usage
    - KV cache memory with configurable dtype
    - Activation memory requirements
    - Attention mechanism detection (MHA, GQA, MLA)
    - Total memory breakdown
    
    Args:
        request: Memory analysis request with models and configuration
        
    Returns:
        MemoryAnalysisResponse with detailed memory breakdown for each model
        
    Raises:
        HTTPException: If analysis fails or models are not supported
    """
    try:
        start_time = datetime.now()
        
        # Validate request
        if not request.model_names:
            raise HTTPException(
                status_code=400,
                detail="At least one model name must be provided"
            )
        
        # Create request ID
        request_id = f"memory_analysis_{int(start_time.timestamp())}_{hash(str(request.model_names))}"
        
        logger.info(f"Starting memory analysis for models: {request.model_names}")
        
        model_results = {}
        
        for model_name in request.model_names:
            try:
                # Create model instance
                model = model_factory.create_model(model_name)
                
                # Get memory breakdown with mixed precision support
                memory_breakdown = model.compute_memory_requirements(
                    sequence_length=request.sequence_length,
                    batch_size=request.batch_size,
                    dtype=request.dtype,
                    weight_dtype=request.weight_dtype,
                    activation_dtype=request.activation_dtype,
                    grad_dtype=request.grad_dtype,
                    optimizer_dtype=request.optimizer_dtype,
                    kv_cache_dtype=request.kv_cache_dtype,
                    training=request.training,
                    include_kv_cache=request.include_kv_cache
                )
                
                # Get attention mechanism type
                attention_mechanism = model.get_attention_mechanism_type()
                
                # Create memory breakdown response
                model_results[model_name] = MemoryBreakdown(
                    parameters=memory_breakdown.get('parameters', 0),
                    kv_cache=memory_breakdown.get('kv_cache', 0),
                    activations=memory_breakdown.get('activations', 0),
                    gradients=memory_breakdown.get('gradients') if request.training else None,
                    optimizer_states=memory_breakdown.get('optimizer_states') if request.training else None,
                    total=memory_breakdown.get('total', 0),
                    dtype=memory_breakdown.get('dtype', request.dtype),
                    dtypes=memory_breakdown.get('dtypes'),
                    attention_mechanism=attention_mechanism,
                    training=request.training
                )
                
            except Exception as e:
                logger.error(f"Error analyzing memory for {model_name}: {e}")
                # Create placeholder breakdown for failed models
                model_results[model_name] = MemoryBreakdown(
                    parameters=0,
                    kv_cache=0,
                    activations=0,
                    gradients=0 if request.training else None,
                    optimizer_states=0 if request.training else None,
                    total=0,
                    dtype=request.dtype,
                    dtypes=None,
                    attention_mechanism="Unknown",
                    training=request.training
                )
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        response = MemoryAnalysisResponse(
            model_results=model_results,
            execution_time=execution_time,
            timestamp=end_time,
            request_id=request_id
        )
        
        logger.info(f"Memory analysis completed in {execution_time:.2f} seconds")
        return response
        
    except ModelNotSupportedError as e:
        logger.error(f"Model not supported: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Model not supported: {str(e)}"
        )
    except ConfigurationError as e:
        logger.error(f"Configuration error: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Configuration error: {str(e)}"
        )
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error during memory analysis: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@app.post("/api/memory/kv-growth", response_model=MemoryAnalysisResponse)
async def analyze_kv_growth(
    request: KVGrowthAnalysisRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
) -> MemoryAnalysisResponse:
    """
    Analyze KV cache memory growth across sequence lengths.
    
    This endpoint analyzes how KV cache memory grows with sequence length,
    providing insights into the memory efficiency of different attention mechanisms.
    
    Args:
        request: KV growth analysis request with sequence length range
        
    Returns:
        MemoryAnalysisResponse with KV growth data for each model
        
    Raises:
        HTTPException: If analysis fails or models are not supported
    """
    try:
        start_time = datetime.now()
        
        # Validate request
        if not request.model_names:
            raise HTTPException(
                status_code=400,
                detail="At least one model name must be provided"
            )
        
        if request.max_sequence_length <= request.min_sequence_length:
            raise HTTPException(
                status_code=400,
                detail="max_sequence_length must be greater than min_sequence_length"
            )
        
        # Create request ID
        request_id = f"kv_growth_{int(start_time.timestamp())}_{hash(str(request.model_names))}"
        
        logger.info(f"Starting KV growth analysis for models: {request.model_names}")
        
        # Generate sequence length range
        sequence_lengths = list(range(
            request.min_sequence_length,
            request.max_sequence_length + 1,
            request.sequence_length_step
        ))
        
        model_results = {}
        kv_growth_data = {}
        
        for model_name in request.model_names:
            try:
                # Create model instance
                model = model_factory.create_model(model_name)
                
                # Get model configuration for memory calculations
                config_dict = model._parsed_config if hasattr(model, '_parsed_config') else {}
                total_params = model.get_total_params()
                
                # Analyze memory growth across sequence lengths
                from ..metrics.memory_calculator import MemoryCalculator
                growth_analysis = MemoryCalculator.analyze_memory_growth_by_sequence_length(
                    config_dict, total_params, sequence_lengths, 
                    request.batch_size, request.dtype
                )
                
                # Convert to KVGrowthPoint objects
                growth_points = []
                for data_point in growth_analysis['memory_data']:
                    memory_bytes = data_point['kv_cache_memory']
                    memory_human = MemoryCalculator._format_memory(memory_bytes)
                    
                    growth_points.append(KVGrowthPoint(
                        sequence_length=data_point['sequence_length'],
                        memory_bytes=memory_bytes,
                        memory_human=memory_human
                    ))
                
                kv_growth_data[model_name] = growth_points
                
                # Create summary memory breakdown for the maximum sequence length
                max_seq_memory = growth_analysis['memory_data'][-1] if growth_analysis['memory_data'] else {}
                attention_mechanism = growth_analysis.get('attention_mechanism', {}).get('type', 'Unknown')
                
                model_results[model_name] = MemoryBreakdown(
                    parameters=max_seq_memory.get('parameter_memory', 0),
                    kv_cache=max_seq_memory.get('kv_cache_memory', 0),
                    activations=max_seq_memory.get('activation_memory', 0),
                    total=max_seq_memory.get('total_memory', 0),
                    dtype=request.dtype,
                    attention_mechanism=attention_mechanism
                )
                
            except Exception as e:
                logger.error(f"Error analyzing KV growth for {model_name}: {e}")
                # Create placeholder data for failed models
                model_results[model_name] = MemoryBreakdown(
                    parameters=0,
                    kv_cache=0,
                    activations=0,
                    total=0,
                    dtype=request.dtype,
                    attention_mechanism="Unknown"
                )
                kv_growth_data[model_name] = []
        
        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()
        
        response = MemoryAnalysisResponse(
            model_results=model_results,
            kv_growth_data=kv_growth_data,
            execution_time=execution_time,
            timestamp=end_time,
            request_id=request_id
        )
        
        logger.info(f"KV growth analysis completed in {execution_time:.2f} seconds")
        return response
        
    except ModelNotSupportedError as e:
        logger.error(f"Model not supported: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Model not supported: {str(e)}"
        )
    except ConfigurationError as e:
        logger.error(f"Configuration error: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Configuration error: {str(e)}"
        )
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(
            status_code=400,
            detail=f"Validation error: {str(e)}"
        )
    except Exception as e:
        logger.error(f"Unexpected error during KV growth analysis: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error: {str(e)}"
        )


@app.get("/api/memory/dtypes")
async def get_supported_dtypes(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token)
) -> Dict[str, Any]:
    """
    Get list of supported data types for memory calculations.
    
    Returns information about supported dtypes including:
    - Available data types (fp16, bf16, fp32, int8)
    - Bytes per element for each dtype
    - Recommended use cases
    
    Returns:
        Dictionary with supported dtype information
    """
    try:
        from ..metrics.memory_calculator import MemoryCalculator
        
        # Get supported dtypes from MemoryCalculator
        precision_bytes = MemoryCalculator.PRECISION_BYTES
        
        # Filter to supported dtypes for memory analysis
        supported_dtypes = ['fp16', 'bf16', 'fp32', 'int8']
        
        dtype_info = {}
        for dtype in supported_dtypes:
            bytes_per_element = precision_bytes.get(dtype, 2)
            
            # Add description and use case information
            if dtype == 'fp32':
                description = "32-bit floating point"
                use_case = "Highest precision, largest memory usage"
            elif dtype == 'fp16':
                description = "16-bit floating point"
                use_case = "Good balance of precision and memory efficiency"
            elif dtype == 'bf16':
                description = "16-bit brain floating point"
                use_case = "Better numerical stability than fp16"
            elif dtype == 'int8':
                description = "8-bit integer"
                use_case = "Quantized models, lowest memory usage"
            else:
                description = f"{dtype} precision"
                use_case = "General purpose"
            
            dtype_info[dtype] = {
                "bytes_per_element": bytes_per_element,
                "description": description,
                "use_case": use_case,
                "memory_multiplier": bytes_per_element / 2.0  # Relative to fp16
            }
        
        return {
            "supported_dtypes": supported_dtypes,
            "default_dtype": "fp16",
            "dtype_info": dtype_info,
            "total_supported": len(supported_dtypes)
        }
        
    except Exception as e:
        logger.error(f"Error getting supported dtypes: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving supported dtypes: {str(e)}"
        )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Custom HTTP exception handler with detailed error information."""
    client_ip = get_client_ip(request)
    
    # Log the error with context
    logger.warning(
        f"HTTP {exc.status_code} error from {client_ip}: {exc.detail} "
        f"[{request.method} {request.url}]"
    )
    
    # Create detailed error response
    error_response = ErrorResponse(
        error=exc.detail,
        status_code=exc.status_code,
        timestamp=datetime.now(),
        request_id=f"error_{int(time.time())}_{hash(str(request.url))}"
    )
    
    # Add rate limit specific headers
    if exc.status_code == 429:
        headers = {"Retry-After": str(RATE_LIMIT_WINDOW)}
    else:
        headers = {}
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump(),
        headers=headers
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """General exception handler for unhandled errors with comprehensive logging."""
    client_ip = get_client_ip(request)
    error_id = f"error_{int(time.time())}_{hash(str(exc))}"
    
    # Log detailed error information
    logger.error(
        f"Unhandled exception {error_id} from {client_ip}: {type(exc).__name__}: {exc} "
        f"[{request.method} {request.url}]"
    )
    logger.error(f"Full traceback for {error_id}:\n{traceback.format_exc()}")
    
    # Determine if this is a known error type
    error_message = "Internal server error"
    details = None
    
    if isinstance(exc, (ModelNotSupportedError, ConfigurationError)):
        error_message = f"Model error: {str(exc)}"
        details = type(exc).__name__
    elif isinstance(exc, ValueError):
        error_message = f"Validation error: {str(exc)}"
        details = "ValueError"
    elif isinstance(exc, ConnectionError):
        error_message = "External service connection error"
        details = "ConnectionError"
    elif isinstance(exc, TimeoutError):
        error_message = "Request timeout"
        details = "TimeoutError"
    else:
        # For unknown errors, include details only in debug mode
        if os.getenv("DEBUG", "false").lower() == "true":
            details = f"{type(exc).__name__}: {str(exc)}"
    
    error_response = ErrorResponse(
        error=error_message,
        status_code=500,
        timestamp=datetime.now(),
        details=details,
        request_id=error_id
    )
    
    return JSONResponse(
        status_code=500,
        content=error_response.model_dump()
    )


# Add middleware for request logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Middleware to log all requests with timing information."""
    start_time = time.time()
    client_ip = get_client_ip(request)
    
    # Log request start
    logger.info(f"Request started: {request.method} {request.url} from {client_ip}")
    
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        
        # Log successful response
        logger.info(
            f"Request completed: {request.method} {request.url} "
            f"-> {response.status_code} ({process_time:.3f}s)"
        )
        
        # Add timing header
        response.headers["X-Process-Time"] = str(process_time)
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        logger.error(
            f"Request failed: {request.method} {request.url} "
            f"-> {type(e).__name__}: {e} ({process_time:.3f}s)"
        )
        raise


# Add startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Application startup event."""
    logger.info("LLM Modeling Metrics API starting up...")
    logger.info(f"Rate limiting: {RATE_LIMIT_REQUESTS} requests per {RATE_LIMIT_WINDOW}s")
    logger.info(f"Authentication: {'Enabled' if API_TOKEN else 'Disabled'}")
    logger.info(f"Supported architectures: {model_factory.get_supported_architectures()}")


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event."""
    logger.info("LLM Modeling Metrics API shutting down...")
    
    # Close all WebSocket connections
    for client_id in list(manager.active_connections.keys()):
        try:
            await manager.active_connections[client_id].close()
        except Exception as e:
            logger.error(f"Error closing WebSocket for {client_id}: {e}")
        finally:
            manager.disconnect(client_id)
    
    logger.info("Shutdown complete")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)