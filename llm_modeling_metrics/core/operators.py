"""
Operator base classes for modeling LLM operations similar to Table 2 in the paper.
This provides a structured way to model different types of operations (Attention, MatMul, Communication, etc.)
with their computational and memory characteristics.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Op<PERSON>, Tuple, List
from dataclasses import dataclass
import yaml
import os


@dataclass
class OperatorMetrics:
    """Metrics for a single operator execution."""
    flops: int = 0
    memory_capacity_bytes: int = 0
    memory_movement_bytes: int = 0
    execution_time_ms: float = 0.0
    arithmetic_intensity: float = 0.0  # FLOPs per byte moved
    utilization: float = 0.0  # Hardware utilization percentage
    
    def __post_init__(self):
        """Calculate derived metrics."""
        if self.memory_movement_bytes > 0:
            self.arithmetic_intensity = self.flops / self.memory_movement_bytes


@dataclass 
class HardwareSpecs:
    """Hardware specifications for performance modeling."""
    name: str
    peak_flops: Dict[str, float]  # TFLOPS for different precisions
    memory_bandwidth_gbps: float
    memory_size_gb: int
    tensor_cores: Optional[int] = None
    
    @classmethod
    def from_gpu_spec(cls, gpu_name: str, gpu_specs_path: str = None) -> 'HardwareSpecs':
        """Load hardware specs from GPU specifications file."""
        if gpu_specs_path is None:
            # Try multiple possible paths
            current_dir = os.path.dirname(os.path.abspath(__file__))
            possible_paths = [
                os.path.join(current_dir, '../../gpu/data/gpu_specs.yaml'),
                os.path.join(current_dir, '../../../gpu/data/gpu_specs.yaml'),
                'gpu/data/gpu_specs.yaml',
                'gpu_specs.yaml'
            ]
            
            gpu_specs_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    gpu_specs_path = path
                    break
            
            if gpu_specs_path is None:
                raise FileNotFoundError("Could not find gpu_specs.yaml file")
        
        with open(gpu_specs_path, 'r') as f:
            specs = yaml.safe_load(f)
        
        gpu_spec = specs['gpus'].get(gpu_name)
        if not gpu_spec:
            raise ValueError(f"GPU {gpu_name} not found in specifications")
        
        # Extract peak FLOPS for different precisions
        peak_flops = {}
        if 'tensor_performance' in gpu_spec:
            for precision, flops in gpu_spec['tensor_performance'].items():
                if flops is not None:
                    peak_flops[precision] = flops
        
        if 'vector_performance' in gpu_spec:
            for precision, flops in gpu_spec['vector_performance'].items():
                if flops is not None:
                    peak_flops[precision] = flops
        
        return cls(
            name=gpu_spec['name'],
            peak_flops=peak_flops,
            memory_bandwidth_gbps=gpu_spec['memory_bandwidth_gbps'],
            memory_size_gb=gpu_spec['memory_size_gb'],
            tensor_cores=gpu_spec.get('tensor_cores_total')
        )


class BaseOperator(ABC):
    """
    Base class for all operators in the LLM modeling system.
    
    Each operator represents a specific computation (e.g., attention, matrix multiplication)
    and can compute its resource requirements and performance characteristics.
    """
    
    def __init__(self, name: str, precision: str = 'fp16'):
        self.name = name
        self.precision = precision
    
    @abstractmethod
    def compute_flops(self, **kwargs) -> int:
        """Compute the number of floating point operations."""
        pass
    
    @abstractmethod
    def compute_memory_capacity_bytes(self, **kwargs) -> int:
        """Compute memory capacity requirements in bytes (storage footprint)."""
        pass
    
    @abstractmethod
    def compute_memory_movement_bytes(self, **kwargs) -> int:
        """Compute memory movement requirements in bytes (data transfer)."""
        pass

    @abstractmethod
    def compute_params(self, **kwargs) -> int:
        """Compute the number of parameters in this operator."""
        pass
    
    def compute_metrics(self, hardware: HardwareSpecs, **kwargs) -> OperatorMetrics:
        """Compute comprehensive metrics for this operator on given hardware."""
        flops = self.compute_flops(**kwargs)
        memory_capacity_bytes = self.compute_memory_capacity_bytes(**kwargs)
        memory_movement_bytes = self.compute_memory_movement_bytes(**kwargs)
        
        # Estimate execution time based on compute vs memory bound
        compute_time_ms = self._estimate_compute_time(flops, hardware)
        memory_time_ms = self._estimate_memory_time(memory_movement_bytes, hardware)
        execution_time_ms = max(compute_time_ms, memory_time_ms)
        
        # Estimate utilization
        utilization = self._estimate_utilization(flops, memory_movement_bytes, hardware)
        
        return OperatorMetrics(
            flops=flops,
            memory_capacity_bytes=memory_capacity_bytes,
            memory_movement_bytes=memory_movement_bytes,
            execution_time_ms=execution_time_ms,
            utilization=utilization
        )
    
    def _estimate_compute_time(self, flops: int, hardware: HardwareSpecs) -> float:
        """Estimate compute time in milliseconds."""
        # Get peak FLOPS for this precision
        peak_flops_key = f"{self.precision}_tensor" if f"{self.precision}_tensor" in hardware.peak_flops else self.precision
        peak_flops = hardware.peak_flops.get(peak_flops_key, hardware.peak_flops.get('fp16', 100))
        
        # Convert TFLOPS to FLOPS and compute time
        peak_flops_per_sec = peak_flops * 1e12
        return (flops / peak_flops_per_sec) * 1000  # Convert to milliseconds
    
    def _estimate_memory_time(self, memory_movement_bytes: int, hardware: HardwareSpecs) -> float:
        """Estimate memory transfer time in milliseconds."""
        # Convert GB/s to bytes/s
        bandwidth_bytes_per_sec = hardware.memory_bandwidth_gbps * 1e9
        return (memory_movement_bytes / bandwidth_bytes_per_sec) * 1000  # Convert to milliseconds
    
    def _estimate_utilization(self, flops: int, memory_movement_bytes: int, hardware: HardwareSpecs) -> float:
        """Estimate hardware utilization percentage."""
        compute_time = self._estimate_compute_time(flops, hardware)
        memory_time = self._estimate_memory_time(memory_movement_bytes, hardware)
        
        if compute_time > memory_time:
            # Compute bound - utilization based on achieved vs peak FLOPS
            return min(100.0, (compute_time / (compute_time + 0.1)) * 100)
        else:
            # Memory bound - utilization based on memory bandwidth
            return min(100.0, (memory_time / (memory_time + 0.1)) * 100)

class MatMulOperator(BaseOperator):
    """
    Matrix multiplication operator for general M x K @ K x N operations.
    """
    
    def __init__(self, M: int, N: int, K: int, precision: str = 'fp16'):
        super().__init__("MatMul", precision)
        self.M = M  # First dimension of input matrix
        self.N = N  # Second dimension of weight matrix (output features)
        self.K = K  # Shared dimension (input features)
    
    def compute_flops(self, **kwargs) -> int:
        """Compute FLOPs for matrix multiplication: M x K @ K x N = M x N."""
        return self.M * self.N * self.K
    
    def compute_memory_capacity_bytes(self, **kwargs) -> int:
        """Compute memory capacity requirements (storage footprint)."""
        bytes_per_element = 2 if self.precision in ['fp16', 'bf16'] else 4
        
        # Input tensor: M x K
        input_memory = self.M * self.K * bytes_per_element
        
        # Weight tensor: K x N
        weight_memory = self.K * self.N * bytes_per_element
        
        # Output tensor: M x N
        output_memory = self.M * self.N * bytes_per_element
        
        return input_memory + weight_memory + output_memory
    
    def compute_memory_movement_bytes(self, **kwargs) -> int:
        """Compute memory movement requirements (data transfer)."""
        bytes_per_element = 2 if self.precision in ['fp16', 'bf16'] else 4

        # Read input and weights, write output
        input_reads = self.M * self.K * bytes_per_element
        weight_reads = self.K * self.N * bytes_per_element
        output_writes = self.M * self.N * bytes_per_element

        return input_reads + weight_reads + output_writes

    def compute_params(self, **kwargs) -> int:
        """Compute the number of parameters in the weight matrix."""
        return self.K * self.N

class AttentionOperator(BaseOperator):
    """
    Attention operator modeling multi-head attention computation.
    
    Covers QKV projections, attention computation, and output projection.
    Optimized for decoding scenarios. Supports MHA and GQA.
    """
    
    def __init__(self, hidden_size: int, num_heads: int, num_kv_heads: int = None, 
                 head_dim: int = None, precision: str = 'fp16', batch_size: int = 1, qlen: int = 1):
        super().__init__("Attention", precision)
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.num_kv_heads = num_kv_heads or num_heads
        self.head_dim = head_dim or (hidden_size // num_heads)
        self.batch_size = batch_size
        self.qlen = qlen
        
        # Create MatMul operators as member variables
        M = batch_size * qlen
        H, D = self.num_heads, self.head_dim
        H_kv = self.num_kv_heads
        
        self.q_proj = MatMulOperator(M, H * D, self.hidden_size, self.precision)
        self.k_proj = MatMulOperator(M, H_kv * D, self.hidden_size, self.precision)
        self.v_proj = MatMulOperator(M, H_kv * D, self.hidden_size, self.precision)
        self.o_proj = MatMulOperator(M, self.hidden_size, H * D, self.precision)
    
    def get_matmul_operators(self):
        """Get all MatMul operators for shape inspection."""
        return {
            'q_proj': self.q_proj,
            'k_proj': self.k_proj,
            'v_proj': self.v_proj,
            'o_proj': self.o_proj
        }
    
    def compute_flops(self, batch_size: int = None, qlen: int = None, kv_lens: int = 2048) -> int:
        """Compute FLOPs for attention operation during decoding."""
        # Use provided values or fall back to member variables
        B = batch_size or self.batch_size
        qlen = qlen or self.qlen
        H, D = self.num_heads, self.head_dim
        
        flops = 0
        
        # QKV projections using member variables
        flops += self.q_proj.compute_flops()
        flops += self.k_proj.compute_flops()
        flops += self.v_proj.compute_flops()
        
        # Attention scores: B * H * qlen * kv_lens * D
        attention_scores_flops = B * H * qlen * kv_lens * D
        flops += attention_scores_flops
        
        # Attention values: B * H * qlen * kv_lens * D  
        attention_values_flops = B * H * qlen * kv_lens * D
        flops += attention_values_flops
        
        # Output projection
        flops += self.o_proj.compute_flops()
        
        return flops
    
    def compute_memory_capacity_bytes(self, batch_size: int = None, qlen: int = None, kv_lens: int = 2048) -> int:
        """Compute memory capacity requirements for attention during decoding."""
        # Use provided values or fall back to member variables
        B = batch_size or self.batch_size
        qlen = qlen or self.qlen
        H, D = self.num_heads, self.head_dim
        H_kv = self.num_kv_heads
        
        bytes_per_element = 2 if self.precision in ['fp16', 'bf16'] else 4
        memory = 0
        
        # QKV projection memory (weights + activations) using member variables
        memory += self.q_proj.compute_memory_capacity_bytes()
        memory += self.k_proj.compute_memory_capacity_bytes()
        memory += self.v_proj.compute_memory_capacity_bytes()
        
        # Attention scores: B * H * qlen * kv_lens
        attention_scores_memory = B * H * qlen * kv_lens * bytes_per_element
        memory += attention_scores_memory
        
        # KV cache storage: B * kv_lens * 2 * H_kv * D
        kv_cache_memory = B * kv_lens * 2 * H_kv * D * bytes_per_element
        memory += kv_cache_memory
        
        # Output projection memory
        memory += self.o_proj.compute_memory_capacity_bytes()
        
        return memory
    
    def compute_memory_movement_bytes(self, batch_size: int = None, qlen: int = None, kv_lens: int = 2048) -> int:
        """Compute memory movement requirements for attention during decoding."""
        # Use provided values or fall back to member variables
        B = batch_size or self.batch_size
        qlen = qlen or self.qlen
        H, D = self.num_heads, self.head_dim
        H_kv = self.num_kv_heads
        
        bytes_per_element = 2 if self.precision in ['fp16', 'bf16'] else 4
        
        # QKV projection memory movement using member variables
        movement = 0
        movement += self.q_proj.compute_memory_movement_bytes()
        movement += self.k_proj.compute_memory_movement_bytes()
        movement += self.v_proj.compute_memory_movement_bytes()
        
        # KV cache reads: B * kv_lens * 2 * H_kv * D
        kv_cache_reads = B * kv_lens * 2 * H_kv * D * bytes_per_element
        movement += kv_cache_reads
        
        # Output projection memory movement
        movement += self.o_proj.compute_memory_movement_bytes()
        
        return movement

    def compute_params(self, **kwargs) -> int:
        """Compute the number of parameters in attention layers."""
        # Sum parameters from all MatMul operators
        return (self.q_proj.compute_params() +
                self.k_proj.compute_params() +
                self.v_proj.compute_params() +
                self.o_proj.compute_params())


class MLAAttentionOperator(BaseOperator):
    """
    Multi-head Latent Attention (MLA) operator for DeepSeek V3.
    
    MLA uses compressed KV representations with LoRA-style projections
    to reduce memory usage while maintaining performance.
    """
    
    def __init__(self, hidden_size: int, num_heads: int, 
                 kv_lora_rank: int = 512, q_lora_rank: int = 1536,
                 qk_rope_head_dim: int = 64, qk_nope_head_dim: int = 128,
                 v_head_dim: int = 128, precision: str = 'fp16',
                 batch_size: int = 1, qlen: int = 1):
        super().__init__("MLAAttention", precision)
        self.hidden_size = hidden_size
        self.num_heads = num_heads
        self.kv_lora_rank = kv_lora_rank
        self.q_lora_rank = q_lora_rank
        self.qk_rope_head_dim = qk_rope_head_dim
        self.qk_nope_head_dim = qk_nope_head_dim
        self.v_head_dim = v_head_dim
        self.batch_size = batch_size
        self.qlen = qlen
        
        # Total head dimensions
        self.qk_head_dim = qk_rope_head_dim + qk_nope_head_dim
        self.total_kv_dim = num_heads * (qk_rope_head_dim + qk_nope_head_dim + v_head_dim)
        
        # Create MatMul operators as member variables
        M = batch_size * qlen
        self.q_compress = MatMulOperator(M, self.q_lora_rank, self.hidden_size, self.precision)
        self.q_decompress = MatMulOperator(M, self.num_heads * self.qk_head_dim, self.q_lora_rank, self.precision)
        self.kv_compress = MatMulOperator(M, self.kv_lora_rank, self.hidden_size, self.precision)
        self.kv_decompress = MatMulOperator(M, self.total_kv_dim, self.kv_lora_rank, self.precision)
        self.o_proj = MatMulOperator(M, self.hidden_size, self.num_heads * self.v_head_dim, self.precision)
    
    def get_matmul_operators(self):
        """Get all MatMul operators for shape inspection."""
        return {
            'q_compress': self.q_compress,
            'q_decompress': self.q_decompress,
            'kv_compress': self.kv_compress,
            'kv_decompress': self.kv_decompress,
            'o_proj': self.o_proj
        }
    
    def compute_flops(self, batch_size: int = None, qlen: int = None, kv_lens: int = 2048) -> int:
        """Compute FLOPs for MLA attention operation."""
        # Use provided values or fall back to member variables
        B = batch_size or self.batch_size
        S = qlen or self.qlen
        
        flops = 0
        
        # 1. Q projection using member MatMul operators
        flops += self.q_compress.compute_flops() + self.q_decompress.compute_flops()
        
        # 2. KV compression using member MatMul operator
        flops += self.kv_compress.compute_flops()
        
        # 3. KV decompression using member MatMul operator
        flops += self.kv_decompress.compute_flops()
        
        # 4. Attention computation: QK^T and softmax(QK^T)V
        # QK^T: B * num_heads * S * kv_lens * qk_head_dim
        qk_flops = B * self.num_heads * S * kv_lens * self.qk_head_dim
        # softmax(QK^T)V: B * num_heads * S * kv_lens * v_head_dim
        qkv_flops = B * self.num_heads * S * kv_lens * self.v_head_dim
        flops += qk_flops + qkv_flops
        
        # 5. Output projection using member MatMul operator
        flops += self.o_proj.compute_flops()
        
        return flops
    
    def compute_linear_projection_flops(self, batch_size: int = None, qlen: int = None) -> int:
        """Compute FLOPs for linear projections only (for Table 2 breakdown)."""
        # Use member MatMul operators
        return (self.q_compress.compute_flops() + self.q_decompress.compute_flops() + 
                self.kv_compress.compute_flops() + self.kv_decompress.compute_flops() + 
                self.o_proj.compute_flops())
    
    def compute_attention_computation_flops(self, batch_size: int = None, qlen: int = None, kv_lens: int = 2048) -> int:
        """Compute FLOPs for attention computation only (for Table 2 breakdown)."""
        # Use provided values or fall back to member variables
        B = batch_size or self.batch_size
        S = qlen or self.qlen
        
        # QK^T: B * num_heads * S * kv_lens * qk_head_dim
        qk_flops = B * self.num_heads * S * kv_lens * self.qk_head_dim
        # softmax(QK^T)V: B * num_heads * S * kv_lens * v_head_dim
        qkv_flops = B * self.num_heads * S * kv_lens * self.v_head_dim
        
        return qk_flops + qkv_flops
    
    def compute_memory_capacity_bytes(self, batch_size: int = None, qlen: int = None, kv_lens: int = 2048) -> int:
        """Compute memory capacity requirements for MLA attention."""
        # Use provided values or fall back to member variables
        B = batch_size or self.batch_size
        S = qlen or self.qlen
        
        memory = 0
        
        # Memory from member MatMul operators (weights + activations)
        memory += self.q_compress.compute_memory_capacity_bytes()
        memory += self.q_decompress.compute_memory_capacity_bytes()
        memory += self.kv_compress.compute_memory_capacity_bytes()
        memory += self.kv_decompress.compute_memory_capacity_bytes()
        memory += self.o_proj.compute_memory_capacity_bytes()
        
        # Additional MLA-specific memory
        bytes_per_element = 2 if self.precision in ['fp16', 'bf16'] else 4
        
        # Compressed KV cache: B * kv_lens * kv_lora_rank
        compressed_kv_cache = B * kv_lens * self.kv_lora_rank * bytes_per_element
        memory += compressed_kv_cache
        
        # Attention scores: B * num_heads * S * kv_lens
        attention_scores = B * self.num_heads * S * kv_lens * bytes_per_element
        memory += attention_scores
        
        return memory
    
    def compute_memory_movement_bytes(self, batch_size: int = None, qlen: int = None, kv_lens: int = 2048) -> int:
        """Compute memory movement requirements for MLA attention."""
        # Use provided values or fall back to member variables
        B = batch_size or self.batch_size
        S = qlen or self.qlen
        
        movement = 0
        
        # Memory movement from member MatMul operators
        movement += self.q_compress.compute_memory_movement_bytes()
        movement += self.q_decompress.compute_memory_movement_bytes()
        movement += self.kv_compress.compute_memory_movement_bytes()
        movement += self.kv_decompress.compute_memory_movement_bytes()
        movement += self.o_proj.compute_memory_movement_bytes()
        
        # Additional MLA-specific memory movement
        bytes_per_element = 2 if self.precision in ['fp16', 'bf16'] else 4
        
        # Compressed KV cache reads
        compressed_kv_reads = B * kv_lens * self.kv_lora_rank * bytes_per_element
        movement += compressed_kv_reads
        
        return movement

    def compute_params(self, **kwargs) -> int:
        """Compute the number of parameters in MLA attention layers."""
        # Sum parameters from all MatMul operators
        return (self.q_compress.compute_params() +
                self.q_decompress.compute_params() +
                self.kv_compress.compute_params() +
                self.kv_decompress.compute_params() +
                self.o_proj.compute_params())

    def get_projection_operators(self, batch_size: int = None, qlen: int = None):
        """Get the individual MatMul operators for detailed analysis."""
        # Return member variables directly
        return self.get_matmul_operators()
    
    def get_projection_breakdown(self, batch_size: int = None, qlen: int = None):
        """Get detailed breakdown of each projection's FLOPs and memory."""
        operators = self.get_matmul_operators()
        
        breakdown = {}
        for name, op in operators.items():
            breakdown[name] = {
                'flops': op.compute_flops(),
                'memory_capacity': op.compute_memory_capacity_bytes(),
                'memory_movement': op.compute_memory_movement_bytes(),
                'weight_params': op.K * op.N,  # Weight matrix size
                'description': self._get_projection_description(name)
            }
        
        return breakdown
    
    def _get_projection_description(self, name: str) -> str:
        """Get human-readable description of each projection."""
        descriptions = {
            'q_compress': f'Query compression: {self.hidden_size} → {self.q_lora_rank}',
            'q_decompress': f'Query decompression: {self.q_lora_rank} → {self.num_heads * self.qk_head_dim}',
            'kv_compress': f'KV compression: {self.hidden_size} → {self.kv_lora_rank}',
            'kv_decompress': f'KV decompression: {self.kv_lora_rank} → {self.total_kv_dim}',
            'o_proj': f'Output projection: {self.num_heads * self.v_head_dim} → {self.hidden_size}'
        }
        return descriptions.get(name, 'Unknown projection')


def extract_all_matmul_operators(operators):
    """
    Extract all MatMul operators from a list of operators for shape inspection.
    
    Args:
        operators: List of operator instances
        
    Returns:
        Dict mapping operator_name.matmul_name to MatMul operator
    """
    all_matmuls = {}
    
    for i, op in enumerate(operators):
        if hasattr(op, 'get_matmul_operators'):
            # Get MatMul operators from composite operators
            matmuls = op.get_matmul_operators()
            for name, matmul_op in matmuls.items():
                key = f"{op.name}_{i}.{name}"
                all_matmuls[key] = matmul_op
        elif isinstance(op, MatMulOperator):
            # Direct MatMul operator
            key = f"{op.name}_{i}"
            all_matmuls[key] = op
    
    return all_matmuls


def print_matmul_shapes(operators, title="MatMul Operator Shapes"):
    """
    Print all MatMul operator shapes in a model.
    
    Args:
        operators: List of operator instances
        title: Title for the output
    """
    all_matmuls = extract_all_matmul_operators(operators)
    
    print(f"=== {title} ===")
    print(f"{'Operator':<30} {'Shape (M x K @ K x N)':<25} {'FLOPs (M)':<12} {'Params (M)':<12}")
    print("-" * 85)
    
    total_flops = 0
    total_params = 0
    
    for name, matmul in all_matmuls.items():
        shape_str = f"{matmul.M} x {matmul.K} @ {matmul.K} x {matmul.N}"
        flops_m = matmul.compute_flops() / 1e6
        params_m = (matmul.K * matmul.N) / 1e6
        
        total_flops += matmul.compute_flops()
        total_params += matmul.K * matmul.N
        
        print(f"{name:<30} {shape_str:<25} {flops_m:<12.2f} {params_m:<12.2f}")
    
    print("-" * 85)
    print(f"{'TOTAL':<30} {'':<25} {total_flops/1e6:<12.2f} {total_params/1e6:<12.2f}")
    print()



class MLPOperator(BaseOperator):
    """
    MLP operator for feed-forward networks with gated activation (SwiGLU, etc.).
    Optimized for decoding scenarios.
    """
    
    def __init__(self, hidden_size: int, intermediate_size: int, precision: str = 'fp16',
                 batch_size: int = 1, qlen: int = 1):
        super().__init__("MLP", precision)
        self.hidden_size = hidden_size
        self.intermediate_size = intermediate_size
        self.batch_size = batch_size
        self.qlen = qlen
        
        # Create MatMul operators as member variables
        M = batch_size * qlen
        self.gate_proj = MatMulOperator(M, self.intermediate_size, self.hidden_size, self.precision)
        self.up_proj = MatMulOperator(M, self.intermediate_size, self.hidden_size, self.precision)
        self.down_proj = MatMulOperator(M, self.hidden_size, self.intermediate_size, self.precision)
    
    def get_matmul_operators(self):
        """Get all MatMul operators for shape inspection."""
        return {
            'gate_proj': self.gate_proj,
            'up_proj': self.up_proj,
            'down_proj': self.down_proj
        }
    
    def compute_flops(self, batch_size: int = None, qlen: int = None, kv_lens: int = None) -> int:
        """Compute FLOPs for MLP during decoding."""
        # Use provided values or fall back to member variables
        B = batch_size or self.batch_size
        S = qlen or self.qlen
        M = B * S
        
        # Gate and up projections using member variables
        gate_flops = self.gate_proj.compute_flops()
        up_flops = self.up_proj.compute_flops()
        
        # Down projection
        down_flops = self.down_proj.compute_flops()
        
        # Add activation FLOPs (SwiGLU: gate * silu(up))
        activation_flops = M * self.intermediate_size * 2  # silu + multiply
        
        return gate_flops + up_flops + down_flops + activation_flops
    
    def compute_memory_capacity_bytes(self, batch_size: int = None, qlen: int = None, kv_lens: int = None) -> int:
        """Compute memory capacity requirements for MLP during decoding."""
        # Use provided values or fall back to member variables
        B = batch_size or self.batch_size
        S = qlen or self.qlen
        M = B * S
        
        # Sum memory requirements from member variables
        gate_memory = self.gate_proj.compute_memory_capacity_bytes()
        up_memory = self.up_proj.compute_memory_capacity_bytes()
        down_memory = self.down_proj.compute_memory_capacity_bytes()
        
        # Note: We avoid double-counting shared input/output tensors
        # The projections share the same input, so we only count it once
        bytes_per_element = 2 if self.precision in ['fp16', 'bf16'] else 4
        
        # Subtract duplicate input memory (counted in both gate and up)
        duplicate_input = M * self.hidden_size * bytes_per_element
        
        return gate_memory + up_memory + down_memory - duplicate_input
    
    def compute_memory_movement_bytes(self, batch_size: int = None, qlen: int = None, kv_lens: int = None) -> int:
        """Compute memory movement requirements for MLP during decoding."""
        # Sum memory movement from member variables
        gate_movement = self.gate_proj.compute_memory_movement_bytes()
        up_movement = self.up_proj.compute_memory_movement_bytes()
        down_movement = self.down_proj.compute_memory_movement_bytes()
        
        return gate_movement + up_movement + down_movement

    def compute_params(self, **kwargs) -> int:
        """Compute the number of parameters in MLP layers."""
        # Sum parameters from all MatMul operators
        return (self.gate_proj.compute_params() +
                self.up_proj.compute_params() +
                self.down_proj.compute_params())


class CommunicationOperator(BaseOperator):
    """
    Communication operator for modeling AllReduce, AllGather, etc.
    """
    
    def __init__(self, operation_type: str, data_size_bytes: int, num_devices: int, 
                 bandwidth_gbps: float = 100.0, precision: str = 'fp16'):
        super().__init__(f"Communication_{operation_type}", precision)
        self.operation_type = operation_type
        self.data_size_bytes = data_size_bytes
        self.num_devices = num_devices
        self.bandwidth_gbps = bandwidth_gbps
    
    def compute_flops(self, **kwargs) -> int:
        """Communication operations don't have FLOPs."""
        return 0
    
    def compute_memory_capacity_bytes(self, **kwargs) -> int:
        """Memory capacity for communication buffers (storage footprint)."""
        if self.operation_type.lower() == 'allreduce':
            return self.data_size_bytes * 2  # Input and output buffers
        elif self.operation_type.lower() == 'allgather':
            return self.data_size_bytes * self.num_devices  # Gather all data
        else:
            return self.data_size_bytes
    
    def compute_memory_movement_bytes(self, **kwargs) -> int:
        """Memory movement for communication (data transfer)."""
        return self.data_size_bytes

    def compute_params(self, **kwargs) -> int:
        """Communication operations don't have parameters."""
        return 0

    def compute_communication_time_ms(self) -> float:
        """Compute communication time based on bandwidth and topology."""
        # Simplified model: assume ring topology for AllReduce
        if self.operation_type.lower() == 'allreduce':
            # Ring AllReduce: 2 * (N-1) / N * data_size / bandwidth
            effective_data = 2 * (self.num_devices - 1) / self.num_devices * self.data_size_bytes
        elif self.operation_type.lower() == 'allgather':
            # AllGather: (N-1) / N * data_size / bandwidth
            effective_data = (self.num_devices - 1) / self.num_devices * self.data_size_bytes
        else:
            effective_data = self.data_size_bytes
        
        # Convert bandwidth to bytes/ms
        bandwidth_bytes_per_ms = self.bandwidth_gbps * 1e9 / 1000
        return effective_data / bandwidth_bytes_per_ms


class MoEOperator(BaseOperator):
    """
    Mixture of Experts operator combining routing and expert computation.
    """
    
    def __init__(self, hidden_size: int, intermediate_size: int, num_experts: int, 
                 experts_per_token: int, precision: str = 'fp16'):
        super().__init__("MoE", precision)
        self.hidden_size = hidden_size
        self.intermediate_size = intermediate_size
        self.num_experts = num_experts
        self.experts_per_token = experts_per_token
        
        # Router (M=1 for parameter counting, will be scaled by batch_size * sequence_length in FLOPS)
        self.router = MatMulOperator(M=1, N=num_experts, K=hidden_size, precision=precision)

        # Expert (single expert, will be scaled by experts_per_token)
        self.expert = MLPOperator(hidden_size, intermediate_size, precision, batch_size=1, qlen=1)
    
    def compute_flops(self, batch_size: int = 1, sequence_length: int = 2048) -> int:
        """Compute FLOPs for MoE operation."""
        # Router FLOPs: batch_size * sequence_length * hidden_size * num_experts
        router_flops = batch_size * sequence_length * self.hidden_size * self.num_experts

        # Expert FLOPs (only for active experts)
        expert_flops = self.expert.compute_flops(batch_size=batch_size, qlen=sequence_length)
        active_expert_flops = expert_flops * self.experts_per_token

        # Routing overhead (softmax, top-k selection)
        routing_flops = batch_size * sequence_length * self.num_experts * 3  # Simplified

        return router_flops + active_expert_flops + routing_flops
    
    def compute_memory_capacity_bytes(self, batch_size: int = 1, sequence_length: int = 2048) -> int:
        """Compute memory capacity requirements for MoE (storage footprint)."""
        bytes_per_element = 2 if self.precision in ['fp16', 'bf16'] else 4

        # Router memory (weight parameters)
        router_memory = self.router.compute_memory_capacity_bytes()

        # Expert weights (all experts)
        expert_memory = self.expert.compute_memory_capacity_bytes(batch_size=1, qlen=1)
        expert_weight_memory = expert_memory * self.num_experts

        # Active expert computation memory
        expert_activation_memory = (self.experts_per_token *
                                   batch_size * sequence_length * self.intermediate_size * bytes_per_element)

        return router_memory + expert_weight_memory + expert_activation_memory

    def compute_memory_movement_bytes(self, batch_size: int = 1, sequence_length: int = 2048) -> int:
        """Compute memory movement for MoE (data transfer)."""
        # Router memory movement: batch_size * sequence_length * (hidden_size + num_experts)
        router_bw = batch_size * sequence_length * (self.hidden_size + self.num_experts) * (2 if self.precision in ['fp16', 'bf16'] else 4)

        # Only active experts contribute to bandwidth
        expert_bw = self.expert.compute_memory_movement_bytes(batch_size=batch_size, qlen=sequence_length)
        active_expert_bw = expert_bw * self.experts_per_token

        return router_bw + active_expert_bw

    def compute_params(self, **kwargs) -> int:
        """Compute the number of parameters in MoE operator."""
        # Router parameters
        router_params = self.router.compute_params()

        # All expert parameters (not just active ones for total param count)
        expert_params = self.expert.compute_params()
        total_expert_params = expert_params * self.num_experts

        return router_params + total_expert_params


class LayerNormOperator(BaseOperator):
    """
    Layer normalization operator.
    """
    
    def __init__(self, hidden_size: int, precision: str = 'fp16'):
        super().__init__("LayerNorm", precision)
        self.hidden_size = hidden_size
    
    def compute_flops(self, batch_size: int = 1, sequence_length: int = 2048) -> int:
        """Compute FLOPs for layer normalization (RMSNorm approximation)."""
        # RMSNorm: ~5 FLOPs per element (mean, variance, normalize, scale)
        return batch_size * sequence_length * self.hidden_size * 5
    
    def compute_memory_capacity_bytes(self, batch_size: int = 1, sequence_length: int = 2048) -> int:
        """Compute memory capacity requirements (storage footprint)."""
        bytes_per_element = 2 if self.precision in ['fp16', 'bf16'] else 4
        
        # Input, output, and scale parameters
        return (2 * batch_size * sequence_length * self.hidden_size + self.hidden_size) * bytes_per_element
    
    def compute_memory_movement_bytes(self, batch_size: int = 1, sequence_length: int = 2048) -> int:
        """Compute memory movement requirements (data transfer)."""
        bytes_per_element = 2 if self.precision in ['fp16', 'bf16'] else 4
        
        # Read input and parameters, write output
        return (2 * batch_size * sequence_length * self.hidden_size + self.hidden_size) * bytes_per_element

    def compute_params(self, **kwargs) -> int:
        """Compute the number of parameters in layer normalization."""
        # Layer norm has scale parameters (and optionally bias, but RMSNorm typically doesn't)
        return self.hidden_size