"""
Matrix shape analysis utilities for LLM operations.
"""

from typing import Dict, Any, Tu<PERSON>, List, Optional
from ..core.base_model import ParallelConfig


class ShapeAnalyzer:
    """
    Utility class for analyzing and computing matrix shapes in LLM operations.
    
    Provides detailed shape analysis for attention matrices, MLP weights,
    and their modifications under different parallel strategies.
    """
    
    @staticmethod
    def compute_attention_shapes(hidden_size: int, num_heads: int, num_kv_heads: int, 
                               head_dim: int, sequence_length: int = 2048,
                               batch_size: int = 1) -> Dict[str, Tuple[int, ...]]:
        """
        Compute shapes for attention matrices.
        
        Args:
            hidden_size: Model hidden dimension
            num_heads: Number of attention heads
            num_kv_heads: Number of key-value heads (for GQA)
            head_dim: Dimension per attention head
            sequence_length: Input sequence length
            batch_size: Batch size
            
        Returns:
            Dictionary with attention matrix shapes
        """
        shapes = {}
        
        # Weight matrices
        shapes['q_weight'] = (hidden_size, num_heads * head_dim)
        shapes['k_weight'] = (hidden_size, num_kv_heads * head_dim)
        shapes['v_weight'] = (hidden_size, num_kv_heads * head_dim)
        shapes['o_weight'] = (num_heads * head_dim, hidden_size)
        
        # Activation tensors during forward pass
        shapes['input'] = (batch_size, sequence_length, hidden_size)
        shapes['q_tensor'] = (batch_size, sequence_length, num_heads, head_dim)
        shapes['k_tensor'] = (batch_size, sequence_length, num_kv_heads, head_dim)
        shapes['v_tensor'] = (batch_size, sequence_length, num_kv_heads, head_dim)
        
        # Attention computation tensors
        shapes['attention_scores'] = (batch_size, num_heads, sequence_length, sequence_length)
        shapes['attention_probs'] = (batch_size, num_heads, sequence_length, sequence_length)
        shapes['attention_output'] = (batch_size, sequence_length, num_heads, head_dim)
        shapes['final_output'] = (batch_size, sequence_length, hidden_size)
        
        # For grouped query attention, we need to handle key/value repetition
        if num_kv_heads != num_heads:
            shapes['k_tensor_repeated'] = (batch_size, sequence_length, num_heads, head_dim)
            shapes['v_tensor_repeated'] = (batch_size, sequence_length, num_heads, head_dim)
        
        return shapes
    
    @staticmethod
    def compute_mlp_shapes(hidden_size: int, intermediate_size: int, 
                          sequence_length: int = 2048, batch_size: int = 1,
                          use_gated_activation: bool = True) -> Dict[str, Tuple[int, ...]]:
        """
        Compute shapes for MLP matrices.
        
        Args:
            hidden_size: Model hidden dimension
            intermediate_size: MLP intermediate dimension
            sequence_length: Input sequence length
            batch_size: Batch size
            use_gated_activation: Whether to use gated activation (SwiGLU, etc.)
            
        Returns:
            Dictionary with MLP matrix shapes
        """
        shapes = {}
        
        # Weight matrices
        if use_gated_activation:
            # Gated activation requires two up-projections
            shapes['gate_weight'] = (hidden_size, intermediate_size)
            shapes['up_weight'] = (hidden_size, intermediate_size)
        else:
            # Standard activation uses single up-projection
            shapes['up_weight'] = (hidden_size, intermediate_size)
        
        shapes['down_weight'] = (intermediate_size, hidden_size)
        
        # Activation tensors during forward pass
        shapes['input'] = (batch_size, sequence_length, hidden_size)
        
        if use_gated_activation:
            shapes['gate_output'] = (batch_size, sequence_length, intermediate_size)
            shapes['up_output'] = (batch_size, sequence_length, intermediate_size)
            shapes['gated_output'] = (batch_size, sequence_length, intermediate_size)
        else:
            shapes['up_output'] = (batch_size, sequence_length, intermediate_size)
            shapes['activated_output'] = (batch_size, sequence_length, intermediate_size)
        
        shapes['final_output'] = (batch_size, sequence_length, hidden_size)
        
        return shapes
    
    @staticmethod
    def compute_embedding_shapes(vocab_size: int, hidden_size: int, 
                               sequence_length: int = 2048, batch_size: int = 1,
                               tie_embeddings: bool = False) -> Dict[str, Tuple[int, ...]]:
        """
        Compute shapes for embedding matrices.
        
        Args:
            vocab_size: Vocabulary size
            hidden_size: Model hidden dimension
            sequence_length: Input sequence length
            batch_size: Batch size
            tie_embeddings: Whether input and output embeddings are tied
            
        Returns:
            Dictionary with embedding matrix shapes
        """
        shapes = {}
        
        # Weight matrices
        shapes['token_embeddings'] = (vocab_size, hidden_size)
        
        if not tie_embeddings:
            shapes['lm_head_weight'] = (hidden_size, vocab_size)
        
        # Activation tensors
        shapes['input_ids'] = (batch_size, sequence_length)
        shapes['embedded_input'] = (batch_size, sequence_length, hidden_size)
        shapes['logits'] = (batch_size, sequence_length, vocab_size)
        
        return shapes
    
    @staticmethod
    def apply_tensor_parallelism(shapes: Dict[str, Tuple[int, ...]], 
                               tp_size: int, operation_type: str) -> Dict[str, Tuple[int, ...]]:
        """
        Apply tensor parallelism to matrix shapes.
        
        Args:
            shapes: Original matrix shapes
            tp_size: Tensor parallel size
            operation_type: Type of operation ('attention', 'mlp', 'embeddings')
            
        Returns:
            Modified shapes under tensor parallelism
        """
        if tp_size <= 1:
            return shapes
        
        parallel_shapes = shapes.copy()
        
        if operation_type == 'attention':
            parallel_shapes = ShapeAnalyzer._apply_attention_tp(parallel_shapes, tp_size)
        elif operation_type == 'mlp':
            parallel_shapes = ShapeAnalyzer._apply_mlp_tp(parallel_shapes, tp_size)
        elif operation_type == 'embeddings':
            # Embeddings are typically replicated, not partitioned
            pass
        
        return parallel_shapes
    
    @staticmethod
    def _apply_attention_tp(shapes: Dict[str, Tuple[int, ...]], tp_size: int) -> Dict[str, Tuple[int, ...]]:
        """Apply tensor parallelism to attention shapes."""
        parallel_shapes = shapes.copy()
        
        # Q, K, V weight matrices are column-parallel (split output dimension)
        if 'q_weight' in shapes:
            h, d = shapes['q_weight']
            parallel_shapes['q_weight'] = (h, d // tp_size)
        
        if 'k_weight' in shapes:
            h, d = shapes['k_weight']
            parallel_shapes['k_weight'] = (h, d // tp_size)
        
        if 'v_weight' in shapes:
            h, d = shapes['v_weight']
            parallel_shapes['v_weight'] = (h, d // tp_size)
        
        # Output weight matrix is row-parallel (split input dimension)
        if 'o_weight' in shapes:
            d, h = shapes['o_weight']
            parallel_shapes['o_weight'] = (d // tp_size, h)
        
        # Activation tensors
        if 'q_tensor' in shapes:
            b, s, nh, hd = shapes['q_tensor']
            parallel_shapes['q_tensor'] = (b, s, nh // tp_size, hd)
        
        if 'k_tensor' in shapes:
            b, s, nkv, hd = shapes['k_tensor']
            parallel_shapes['k_tensor'] = (b, s, nkv // tp_size, hd)
        
        if 'v_tensor' in shapes:
            b, s, nkv, hd = shapes['v_tensor']
            parallel_shapes['v_tensor'] = (b, s, nkv // tp_size, hd)
        
        if 'attention_scores' in shapes:
            b, nh, s1, s2 = shapes['attention_scores']
            parallel_shapes['attention_scores'] = (b, nh // tp_size, s1, s2)
        
        if 'attention_probs' in shapes:
            b, nh, s1, s2 = shapes['attention_probs']
            parallel_shapes['attention_probs'] = (b, nh // tp_size, s1, s2)
        
        if 'attention_output' in shapes:
            b, s, nh, hd = shapes['attention_output']
            parallel_shapes['attention_output'] = (b, s, nh // tp_size, hd)
        
        return parallel_shapes
    
    @staticmethod
    def _apply_mlp_tp(shapes: Dict[str, Tuple[int, ...]], tp_size: int) -> Dict[str, Tuple[int, ...]]:
        """Apply tensor parallelism to MLP shapes."""
        parallel_shapes = shapes.copy()
        
        # Gate and up weight matrices are column-parallel
        if 'gate_weight' in shapes:
            h, i = shapes['gate_weight']
            parallel_shapes['gate_weight'] = (h, i // tp_size)
        
        if 'up_weight' in shapes:
            h, i = shapes['up_weight']
            parallel_shapes['up_weight'] = (h, i // tp_size)
        
        # Down weight matrix is row-parallel
        if 'down_weight' in shapes:
            i, h = shapes['down_weight']
            parallel_shapes['down_weight'] = (i // tp_size, h)
        
        # Activation tensors
        if 'gate_output' in shapes:
            b, s, i = shapes['gate_output']
            parallel_shapes['gate_output'] = (b, s, i // tp_size)
        
        if 'up_output' in shapes:
            b, s, i = shapes['up_output']
            parallel_shapes['up_output'] = (b, s, i // tp_size)
        
        if 'gated_output' in shapes:
            b, s, i = shapes['gated_output']
            parallel_shapes['gated_output'] = (b, s, i // tp_size)
        
        if 'activated_output' in shapes:
            b, s, i = shapes['activated_output']
            parallel_shapes['activated_output'] = (b, s, i // tp_size)
        
        return parallel_shapes
    
    @staticmethod
    def validate_tensor_parallel_shapes(shapes: Dict[str, Tuple[int, ...]], 
                                      tp_size: int) -> List[str]:
        """
        Validate that shapes are compatible with tensor parallelism.
        
        Args:
            shapes: Matrix shapes to validate
            tp_size: Tensor parallel size
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        # Check divisibility constraints for common weight matrices
        weight_matrices = {
            'q_weight': 'Q projection output dimension',
            'k_weight': 'K projection output dimension', 
            'v_weight': 'V projection output dimension',
            'gate_weight': 'Gate projection output dimension',
            'up_weight': 'Up projection output dimension'
        }
        
        for matrix_name, description in weight_matrices.items():
            if matrix_name in shapes:
                shape = shapes[matrix_name]
                if len(shape) >= 2:
                    output_dim = shape[1]  # Assuming (input_dim, output_dim)
                    if output_dim % tp_size != 0:
                        errors.append(f"{description} ({output_dim}) not divisible by tensor parallel size ({tp_size})")
        
        # Check row-parallel matrices
        row_parallel_matrices = {
            'o_weight': 'Output projection input dimension',
            'down_weight': 'Down projection input dimension'
        }
        
        for matrix_name, description in row_parallel_matrices.items():
            if matrix_name in shapes:
                shape = shapes[matrix_name]
                if len(shape) >= 2:
                    input_dim = shape[0]  # Assuming (input_dim, output_dim)
                    if input_dim % tp_size != 0:
                        errors.append(f"{description} ({input_dim}) not divisible by tensor parallel size ({tp_size})")
        
        return errors
    
    @staticmethod
    def compute_communication_volume(shapes: Dict[str, Tuple[int, ...]], 
                                   parallel_config: ParallelConfig,
                                   bytes_per_element: int = 2) -> Dict[str, int]:
        """
        Compute communication volume for tensor parallel operations.
        
        Args:
            shapes: Matrix shapes
            parallel_config: Parallel configuration
            bytes_per_element: Bytes per tensor element (e.g., 2 for FP16)
            
        Returns:
            Dictionary with communication volumes by operation type
        """
        tp_size = parallel_config.tensor_parallel_size
        
        if tp_size <= 1:
            return {'total': 0}
        
        comm_volume = {}
        
        # All-reduce operations for row-parallel layers
        all_reduce_volume = 0
        
        # Output projection in attention (row-parallel)
        if 'final_output' in shapes:
            output_elements = 1
            for dim in shapes['final_output']:
                output_elements *= dim
            all_reduce_volume += output_elements * bytes_per_element
        
        # Down projection in MLP (row-parallel)  
        if 'final_output' in shapes:  # MLP final output
            output_elements = 1
            for dim in shapes['final_output']:
                output_elements *= dim
            all_reduce_volume += output_elements * bytes_per_element
        
        comm_volume['all_reduce'] = all_reduce_volume
        
        # All-gather operations (if any)
        comm_volume['all_gather'] = 0
        
        # Total communication volume
        comm_volume['total'] = comm_volume['all_reduce'] + comm_volume['all_gather']
        
        return comm_volume
    
    @staticmethod
    def get_detailed_shape_analysis(model_config: Dict[str, Any], 
                                  sequence_length: int = 2048,
                                  batch_size: int = 1,
                                  parallel_config: Optional[ParallelConfig] = None) -> Dict[str, Any]:
        """
        Get comprehensive shape analysis for a model configuration.
        
        Args:
            model_config: Model configuration dictionary
            sequence_length: Input sequence length
            batch_size: Batch size
            parallel_config: Parallel configuration
            
        Returns:
            Comprehensive shape analysis
        """
        analysis = {}
        
        # Extract configuration parameters
        hidden_size = model_config.get('hidden_size', 0)
        num_heads = model_config.get('num_attention_heads', 0)
        num_kv_heads = model_config.get('num_key_value_heads', num_heads)
        head_dim = hidden_size // num_heads if num_heads > 0 else 0
        intermediate_size = model_config.get('intermediate_size', 0)
        vocab_size = model_config.get('vocab_size', 0)
        tie_embeddings = model_config.get('tie_word_embeddings', False)
        
        # Compute base shapes
        analysis['attention'] = ShapeAnalyzer.compute_attention_shapes(
            hidden_size, num_heads, num_kv_heads, head_dim, sequence_length, batch_size
        )
        
        analysis['mlp'] = ShapeAnalyzer.compute_mlp_shapes(
            hidden_size, intermediate_size, sequence_length, batch_size
        )
        
        analysis['embeddings'] = ShapeAnalyzer.compute_embedding_shapes(
            vocab_size, hidden_size, sequence_length, batch_size, tie_embeddings
        )
        
        # Apply tensor parallelism if specified
        if parallel_config and parallel_config.tensor_parallel_size > 1:
            tp_size = parallel_config.tensor_parallel_size
            
            analysis['attention_tp'] = ShapeAnalyzer.apply_tensor_parallelism(
                analysis['attention'], tp_size, 'attention'
            )
            
            analysis['mlp_tp'] = ShapeAnalyzer.apply_tensor_parallelism(
                analysis['mlp'], tp_size, 'mlp'
            )
            
            analysis['embeddings_tp'] = ShapeAnalyzer.apply_tensor_parallelism(
                analysis['embeddings'], tp_size, 'embeddings'
            )
            
            # Validation
            attention_errors = ShapeAnalyzer.validate_tensor_parallel_shapes(
                analysis['attention'], tp_size
            )
            mlp_errors = ShapeAnalyzer.validate_tensor_parallel_shapes(
                analysis['mlp'], tp_size
            )
            
            analysis['validation_errors'] = attention_errors + mlp_errors
            
            # Communication volume
            analysis['communication'] = {
                'attention': ShapeAnalyzer.compute_communication_volume(
                    analysis['attention'], parallel_config
                ),
                'mlp': ShapeAnalyzer.compute_communication_volume(
                    analysis['mlp'], parallel_config
                )
            }
        
        return analysis