"""
Memory calculation utilities for LLM operations.
"""

from typing import Dict, Any, Optional, List
from ..core.base_model import ParallelConfig


class MemoryCalculator:
    """
    Utility class for computing memory requirements in LLM operations.
    
    Provides detailed memory calculations for parameters, activations, gradients,
    and optimizer states under different parallel strategies and precision settings.
    """
    
    # Memory constants (bytes per element)
    PRECISION_BYTES = {
        'fp32': 4,
        'fp16': 2,
        'bf16': 2,
        'int8': 1,
        'int4': 0.5,
        'fp8': 1,
        'fp4': 0.5
    }
    
    @staticmethod
    def compute_mixed_precision_memory(total_params: int, 
                                     weight_dtype: str = 'fp16',
                                     grad_dtype: str = 'fp16', 
                                     optimizer_dtype: str = 'fp32',
                                     kv_cache_dtype: str = 'fp16',
                                     training: bool = False,
                                     optimizer_type: str = 'adam') -> Dict[str, int]:
        """
        Compute memory requirements with mixed precision support.
        
        Args:
            total_params: Total number of parameters
            weight_dtype: Data type for model weights ('fp32', 'fp16', 'bf16', 'int8', 'fp8', 'fp4')
            grad_dtype: Data type for gradients ('fp32', 'fp16', 'bf16')
            optimizer_dtype: Data type for optimizer states ('fp32', 'fp16', 'bf16')
            kv_cache_dtype: Data type for KV cache ('fp32', 'fp16', 'bf16', 'int8', 'fp8')
            training: Whether this is for training (includes gradients/optimizer)
            optimizer_type: Type of optimizer ('adam', 'sgd', 'adamw')
            
        Returns:
            Dictionary with mixed precision memory breakdown
        """
        memory = {}
        
        # Parameter memory
        weight_bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(weight_dtype, 2)
        param_memory = total_params * weight_bytes_per_param
        memory['parameters'] = param_memory
        memory['weight_dtype'] = weight_dtype
        
        # Gradient memory (only for training)
        if training:
            grad_bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(grad_dtype, 2)
            gradient_memory = total_params * grad_bytes_per_param
            memory['gradients'] = gradient_memory
            memory['grad_dtype'] = grad_dtype
        
        # Optimizer state memory (only for training)
        if training:
            optimizer_bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(optimizer_dtype, 4)
            if optimizer_type.lower() in ['adam', 'adamw']:
                # Adam stores first and second moments
                optimizer_memory = total_params * 2 * optimizer_bytes_per_param
            elif optimizer_type.lower() == 'sgd':
                # SGD with momentum stores momentum buffer
                optimizer_memory = total_params * optimizer_bytes_per_param
            else:
                # Default to Adam-like memory usage
                optimizer_memory = total_params * 2 * optimizer_bytes_per_param
            
            memory['optimizer_states'] = optimizer_memory
            memory['optimizer_dtype'] = optimizer_dtype
        
        # KV cache dtype info (for reference, actual cache memory computed separately)
        memory['kv_cache_dtype'] = kv_cache_dtype
        
        # Total parameter-related memory
        memory['total'] = sum(v for k, v in memory.items() if isinstance(v, int))
        
        return memory

    @staticmethod
    def compute_parameter_memory(total_params: int, precision: str = 'fp16',
                               include_gradients: bool = True,
                               include_optimizer: bool = True,
                               optimizer_type: str = 'adam') -> Dict[str, int]:
        """
        Compute memory requirements for model parameters.
        
        Args:
            total_params: Total number of parameters
            precision: Parameter precision ('fp32', 'fp16', 'bf16', 'int8', 'int4')
            include_gradients: Whether to include gradient memory
            include_optimizer: Whether to include optimizer state memory
            optimizer_type: Type of optimizer ('adam', 'sgd', 'adamw')
            
        Returns:
            Dictionary with parameter memory breakdown
        """
        memory = {}
        
        bytes_per_param = MemoryCalculator.PRECISION_BYTES.get(precision, 2)
        
        # Parameter memory
        param_memory = total_params * bytes_per_param
        memory['parameters'] = param_memory
        
        # Gradient memory (typically same precision as parameters)
        if include_gradients:
            gradient_memory = total_params * bytes_per_param
            memory['gradients'] = gradient_memory
        
        # Optimizer state memory
        if include_optimizer:
            if optimizer_type.lower() in ['adam', 'adamw']:
                # Adam stores first and second moments (both fp32 typically)
                optimizer_memory = total_params * 2 * MemoryCalculator.PRECISION_BYTES['fp32']
            elif optimizer_type.lower() == 'sgd':
                # SGD with momentum stores momentum buffer
                optimizer_memory = total_params * MemoryCalculator.PRECISION_BYTES['fp32']
            else:
                # Default to Adam-like memory usage
                optimizer_memory = total_params * 2 * MemoryCalculator.PRECISION_BYTES['fp32']
            
            memory['optimizer_states'] = optimizer_memory
        
        # Total parameter-related memory
        memory['total'] = sum(memory.values())
        
        return memory
    
    @staticmethod
    def compute_activation_memory(model_config: Dict[str, Any], sequence_length: int = 2048,
                                batch_size: int = 1, precision: str = 'fp16',
                                activation_checkpointing: bool = False) -> Dict[str, int]:
        """
        Compute memory requirements for activations.
        
        Args:
            model_config: Model configuration dictionary
            sequence_length: Input sequence length
            batch_size: Batch size
            precision: Activation precision
            activation_checkpointing: Whether activation checkpointing is used
            
        Returns:
            Dictionary with activation memory breakdown
        """
        memory = {}
        
        bytes_per_element = MemoryCalculator.PRECISION_BYTES.get(precision, 2)
        
        # Extract configuration parameters
        hidden_size = model_config.get('hidden_size', 0)
        num_layers = model_config.get('num_hidden_layers', 0)
        num_heads = model_config.get('num_attention_heads', 0)
        intermediate_size = model_config.get('intermediate_size', 0)
        vocab_size = model_config.get('vocab_size', 0)
        
        # Input embeddings
        input_embedding_memory = batch_size * sequence_length * hidden_size * bytes_per_element
        memory['input_embeddings'] = input_embedding_memory
        
        # Per-layer activation memory
        layer_memory = MemoryCalculator._compute_layer_activation_memory(
            hidden_size, num_heads, intermediate_size, sequence_length, 
            batch_size, bytes_per_element, model_config
        )
        
        if activation_checkpointing:
            # With activation checkpointing, we only store activations for a subset of layers
            # Typically store every sqrt(num_layers) layers
            import math
            checkpointed_layers = max(1, int(math.sqrt(num_layers)))
            total_layer_memory = layer_memory * checkpointed_layers
        else:
            # Without checkpointing, store activations for all layers
            total_layer_memory = layer_memory * num_layers
        
        memory['layer_activations'] = total_layer_memory
        
        # Output layer activations (logits)
        output_memory = batch_size * sequence_length * vocab_size * bytes_per_element
        memory['output_logits'] = output_memory
        
        # Attention matrices (can be very large for long sequences)
        attention_memory = MemoryCalculator._compute_attention_activation_memory(
            num_heads, sequence_length, batch_size, bytes_per_element, num_layers,
            activation_checkpointing
        )
        memory['attention_matrices'] = attention_memory
        
        # Total activation memory
        memory['total'] = sum(memory.values())
        
        return memory
    
    @staticmethod
    def _compute_layer_activation_memory(hidden_size: int, num_heads: int, 
                                       intermediate_size: int, sequence_length: int,
                                       batch_size: int, bytes_per_element: int,
                                       model_config: Dict[str, Any]) -> int:
        """Compute activation memory for a single layer."""
        layer_memory = 0
        
        # Hidden states (input to layer)
        hidden_state_memory = batch_size * sequence_length * hidden_size * bytes_per_element
        layer_memory += hidden_state_memory
        
        # Attention intermediate activations
        head_dim = hidden_size // num_heads
        
        # Q, K, V tensors
        qkv_memory = 3 * batch_size * sequence_length * hidden_size * bytes_per_element
        layer_memory += qkv_memory
        
        # Attention output before projection
        attn_output_memory = batch_size * sequence_length * hidden_size * bytes_per_element
        layer_memory += attn_output_memory
        
        # MLP intermediate activations
        is_moe = 'num_experts' in model_config or 'n_routed_experts' in model_config
        
        if is_moe:
            # For MoE, only activated experts contribute to memory
            experts_per_token = model_config.get('num_experts_per_tok', 
                                               model_config.get('experts_per_token', 2))
            shared_experts = model_config.get('n_shared_experts', 0)
            
            # Router activations
            num_experts = model_config.get('num_experts', model_config.get('n_routed_experts', 0))
            router_memory = batch_size * sequence_length * num_experts * bytes_per_element
            layer_memory += router_memory
            
            # Expert intermediate activations (only for active experts)
            total_tokens = batch_size * sequence_length
            active_expert_memory = (total_tokens * experts_per_token * 
                                  intermediate_size * bytes_per_element * 2)  # gate + up
            layer_memory += active_expert_memory
            
            # Shared expert memory (if any)
            if shared_experts > 0:
                shared_expert_memory = (total_tokens * shared_experts * 
                                      intermediate_size * bytes_per_element * 2)
                layer_memory += shared_expert_memory
        else:
            # Dense MLP
            # Gate and up projections (for gated activations like SwiGLU)
            mlp_intermediate_memory = (batch_size * sequence_length * 
                                     intermediate_size * bytes_per_element * 2)
            layer_memory += mlp_intermediate_memory
        
        return layer_memory
    
    @staticmethod
    def _compute_attention_activation_memory(num_heads: int, sequence_length: int,
                                           batch_size: int, bytes_per_element: int,
                                           num_layers: int, activation_checkpointing: bool) -> int:
        """Compute memory for attention matrices."""
        # Attention scores and probabilities
        # Shape: (batch_size, num_heads, seq_len, seq_len)
        attention_matrix_size = batch_size * num_heads * sequence_length * sequence_length
        attention_memory_per_layer = attention_matrix_size * bytes_per_element * 2  # scores + probs
        
        if activation_checkpointing:
            # With checkpointing, attention matrices are recomputed
            # We only store them for the current layer being processed
            total_attention_memory = attention_memory_per_layer
        else:
            # Without checkpointing, store attention matrices for all layers
            total_attention_memory = attention_memory_per_layer * num_layers
        
        return total_attention_memory
    
    @staticmethod
    def compute_kv_cache_memory(model_config: Dict[str, Any], sequence_length: int = 2048,
                              batch_size: int = 1, precision: str = 'fp16', dtype: Optional[str] = None) -> Dict[str, int]:
        """
        Compute memory requirements for KV cache during inference.
        
        Args:
            model_config: Model configuration dictionary
            sequence_length: Maximum sequence length to cache
            batch_size: Batch size
            precision: Cache precision (deprecated, use dtype instead)
            dtype: Data type for KV cache ('fp16', 'bf16', 'fp32', 'int8')
            
        Returns:
            Dictionary with KV cache memory breakdown
        """
        memory = {}
        
        # Use dtype if provided, otherwise fall back to precision for backward compatibility
        effective_dtype = dtype if dtype is not None else precision
        bytes_per_element = MemoryCalculator.PRECISION_BYTES.get(effective_dtype, 2)
        
        # Extract configuration parameters
        hidden_size = model_config.get('hidden_size', 0)
        num_layers = model_config.get('num_hidden_layers', 0)
        num_heads = model_config.get('num_attention_heads', 0)
        num_kv_heads = model_config.get('num_key_value_heads', num_heads)
        head_dim = hidden_size // num_heads
        
        # KV cache per layer
        # K cache: (batch_size, num_kv_heads, seq_len, head_dim)
        k_cache_per_layer = batch_size * num_kv_heads * sequence_length * head_dim * bytes_per_element
        
        # V cache: (batch_size, num_kv_heads, seq_len, head_dim)
        v_cache_per_layer = batch_size * num_kv_heads * sequence_length * head_dim * bytes_per_element
        
        kv_cache_per_layer = k_cache_per_layer + v_cache_per_layer
        
        memory['k_cache_per_layer'] = k_cache_per_layer
        memory['v_cache_per_layer'] = v_cache_per_layer
        memory['kv_cache_per_layer'] = kv_cache_per_layer
        
        # Total KV cache for all layers
        total_kv_cache = kv_cache_per_layer * num_layers
        memory['total_kv_cache'] = total_kv_cache
        
        # Memory growth per new token
        kv_cache_per_token = batch_size * num_kv_heads * head_dim * bytes_per_element * 2 * num_layers
        memory['kv_cache_per_token'] = kv_cache_per_token
        
        memory['total'] = total_kv_cache
        
        return memory
    
    @staticmethod
    def compute_kv_cache_memory_by_dtype(model_config: Dict[str, Any], sequence_length: int,
                                       batch_size: int = 1, dtype: str = 'fp16') -> Dict[str, int]:
        """
        Compute KV cache memory with configurable dtype.
        
        Args:
            model_config: Model configuration dictionary
            sequence_length: Maximum sequence length to cache
            batch_size: Batch size
            dtype: Data type for KV cache ('fp16', 'bf16', 'fp32', 'int8')
            
        Returns:
            Dictionary with KV cache memory breakdown including dtype info
        """
        memory = MemoryCalculator.compute_kv_cache_memory(
            model_config, sequence_length, batch_size, dtype=dtype
        )
        
        # Add dtype information to the result
        memory['dtype'] = dtype
        memory['bytes_per_element'] = MemoryCalculator.PRECISION_BYTES.get(dtype, 2)
        
        return memory
    
    @staticmethod
    def analyze_memory_growth_by_sequence_length(model_config: Dict[str, Any], total_params: int,
                                               sequence_lengths: list, batch_size: int = 1,
                                               dtype: str = 'fp16') -> Dict[str, Any]:
        """
        Analyze memory growth patterns across sequence lengths.
        
        Args:
            model_config: Model configuration dictionary
            total_params: Total number of parameters
            sequence_lengths: List of sequence lengths to analyze
            batch_size: Batch size
            dtype: Data type for calculations
            
        Returns:
            Dictionary with memory growth analysis including attention mechanism info
        """
        results = {
            'sequence_lengths': sequence_lengths,
            'dtype': dtype,
            'batch_size': batch_size,
            'attention_mechanism': MemoryCalculator.get_attention_mechanism_info(model_config),
            'memory_data': []
        }
        
        for seq_len in sequence_lengths:
            # Compute KV cache memory for this sequence length
            kv_memory = MemoryCalculator.compute_kv_cache_memory_by_dtype(
                model_config, seq_len, batch_size, dtype
            )
            
            # Compute total memory including parameters and activations
            total_memory = MemoryCalculator.compute_total_memory_requirements(
                model_config, total_params, seq_len, batch_size, dtype,
                training=False, include_kv_cache=True
            )

            import logging
            logging.info(f'total_memory={total_memory}')
            
            memory_point = {
                'sequence_length': seq_len,
                'kv_cache_memory': kv_memory['total'],
                'total_memory': total_memory['total'],
                'parameter_memory': total_memory.get('param_parameters', 0),
                'activation_memory': total_memory.get('activation_total', 0),
                'memory_per_token': kv_memory.get('kv_cache_per_token', 0),
                'debug_total_memory_dict': total_memory  # Temporary debug info
            }
            
            results['memory_data'].append(memory_point)
        
        return results
    
    @staticmethod
    def get_attention_mechanism_info(model_config: Dict[str, Any]) -> Dict[str, str]:
        """
        Determine attention mechanism type (MHA, GQA, MLA) from model configuration.
        
        Args:
            model_config: Model configuration dictionary
            
        Returns:
            Dictionary with attention mechanism information
        """
        num_attention_heads = model_config.get('num_attention_heads', 0)
        num_key_value_heads = model_config.get('num_key_value_heads', num_attention_heads)
        
        # Check for MLA (Multi-head Latent Attention) indicators
        # MLA typically has specific configuration keys like kv_lora_rank or qk_rope_head_dim
        mla_indicators = [
            'kv_lora_rank', 'qk_rope_head_dim', 'kv_head_dim', 'qk_nope_head_dim',
            'q_lora_rank', 'kv_a_proj_with_mqa', 'qk_head_dim'
        ]
        
        has_mla_indicators = any(key in model_config and model_config[key] is not None for key in mla_indicators)
        
        if has_mla_indicators:
            mechanism_type = 'MLA'
            description = 'Multi-head Latent Attention'
        elif num_key_value_heads < num_attention_heads and num_key_value_heads > 0:
            mechanism_type = 'GQA'
            description = 'Grouped Query Attention'
        elif num_key_value_heads == num_attention_heads:
            mechanism_type = 'MHA'
            description = 'Multi-Head Attention'
        else:
            mechanism_type = 'Unknown'
            description = 'Unknown attention mechanism'
        
        return {
            'type': mechanism_type,
            'description': description,
            'num_attention_heads': num_attention_heads,
            'num_key_value_heads': num_key_value_heads,
            'head_ratio': num_attention_heads / max(num_key_value_heads, 1) if num_key_value_heads > 0 else 1.0
        }
    
    @staticmethod
    def compute_mixed_precision_total_memory(model_config: Dict[str, Any], total_params: int,
                                           sequence_length: int = 2048, batch_size: int = 1,
                                           weight_dtype: str = 'fp16',
                                           activation_dtype: str = 'fp16',
                                           grad_dtype: str = 'fp16',
                                           optimizer_dtype: str = 'fp32',
                                           kv_cache_dtype: str = 'fp16',
                                           training: bool = False,
                                           activation_checkpointing: bool = False,
                                           include_kv_cache: bool = False,
                                           optimizer_type: str = 'adam') -> Dict[str, int]:
        """
        Compute total memory requirements with mixed precision support.
        
        Args:
            model_config: Model configuration dictionary
            total_params: Total number of parameters
            sequence_length: Input sequence length
            batch_size: Batch size
            weight_dtype: Data type for model weights
            activation_dtype: Data type for activations
            grad_dtype: Data type for gradients (training only)
            optimizer_dtype: Data type for optimizer states (training only)
            kv_cache_dtype: Data type for KV cache (inference only)
            training: Whether this is for training
            activation_checkpointing: Whether activation checkpointing is used
            include_kv_cache: Whether to include KV cache memory (inference only)
            optimizer_type: Type of optimizer
            
        Returns:
            Dictionary with complete mixed precision memory breakdown
        """
        memory = {}
        
        # Mixed precision parameter memory
        param_memory = MemoryCalculator.compute_mixed_precision_memory(
            total_params, weight_dtype, grad_dtype, optimizer_dtype, kv_cache_dtype,
            training, optimizer_type
        )
        memory.update({f'param_{k}': v for k, v in param_memory.items()})
        
        # Activation memory with specified dtype
        activation_memory = MemoryCalculator.compute_activation_memory(
            model_config, sequence_length, batch_size, activation_dtype, activation_checkpointing
        )
        memory.update({f'activation_{k}': v for k, v in activation_memory.items()})
        
        # KV cache memory with specified dtype (for inference)
        if include_kv_cache and not training:
            kv_cache_memory = MemoryCalculator.compute_kv_cache_memory(
                model_config, sequence_length, batch_size, dtype=kv_cache_dtype
            )
            memory.update({f'kv_cache_{k}': v for k, v in kv_cache_memory.items()})
        
        # Calculate total memory
        param_total = memory.get('param_total', 0)
        activation_total = memory.get('activation_total', 0)
        kv_cache_total = memory.get('kv_cache_total', 0) if include_kv_cache and not training else 0
        
        memory['total'] = param_total + activation_total + kv_cache_total
        
        # Add dtype information
        memory['dtypes'] = {
            'weight': weight_dtype,
            'activation': activation_dtype,
            'grad': grad_dtype if training else None,
            'optimizer': optimizer_dtype if training else None,
            'kv_cache': kv_cache_dtype if include_kv_cache and not training else None
        }
        
        return memory

    @staticmethod
    def compute_total_memory_requirements(model_config: Dict[str, Any], total_params: int,
                                        sequence_length: int = 2048, batch_size: int = 1,
                                        precision: str = 'fp16', training: bool = False,
                                        activation_checkpointing: bool = False,
                                        include_kv_cache: bool = False) -> Dict[str, int]:
        """
        Compute total memory requirements for model execution.
        
        Args:
            model_config: Model configuration dictionary
            total_params: Total number of parameters
            sequence_length: Input sequence length
            batch_size: Batch size
            precision: Model precision
            training: Whether this is for training (includes gradients/optimizer)
            activation_checkpointing: Whether activation checkpointing is used
            include_kv_cache: Whether to include KV cache memory (for inference)
            
        Returns:
            Dictionary with complete memory breakdown
        """
        memory = {}
        
        # Parameter memory
        param_memory = MemoryCalculator.compute_parameter_memory(
            total_params, precision, training, training, 'adam'
        )
        memory.update({f'param_{k}': v for k, v in param_memory.items()})
        
        # Activation memory
        activation_memory = MemoryCalculator.compute_activation_memory(
            model_config, sequence_length, batch_size, precision, activation_checkpointing
        )
        memory.update({f'activation_{k}': v for k, v in activation_memory.items()})
        
        # KV cache memory (for inference)
        if include_kv_cache and not training:
            kv_cache_memory = MemoryCalculator.compute_kv_cache_memory(
                model_config, sequence_length, batch_size, precision
            )
            memory.update({f'kv_cache_{k}': v for k, v in kv_cache_memory.items()})
        
        # Total memory
        memory['total'] = memory['param_total'] + memory['activation_total']

        return memory
    
    @staticmethod
    def adjust_memory_for_parallel(memory: Dict[str, int], 
                                 parallel_config: ParallelConfig,
                                 total_params: int) -> Dict[str, int]:
        """
        Adjust memory calculations for parallel execution.
        
        Args:
            memory: Original memory calculations
            parallel_config: Parallel configuration
            total_params: Total number of parameters
            
        Returns:
            Adjusted memory calculations per device
        """
        adjusted_memory = memory.copy()
        
        tp_size = parallel_config.tensor_parallel_size
        pp_size = parallel_config.pipeline_parallel_size
        dp_size = parallel_config.data_parallel_size
        
        # Tensor parallelism: parameters are sharded across devices
        if tp_size > 1:
            # Parameter memory is divided by TP size
            for key in adjusted_memory:
                if 'param_' in key and key != 'param_total':
                    adjusted_memory[f'{key}_per_device'] = adjusted_memory[key] // tp_size
        
        # Pipeline parallelism: layers are distributed across devices
        if pp_size > 1:
            # Activation memory is roughly divided by PP size
            for key in adjusted_memory:
                if 'activation_' in key and 'layer' in key:
                    adjusted_memory[f'{key}_per_device'] = adjusted_memory[key] // pp_size
        
        # Data parallelism: each device processes a subset of the batch
        if dp_size > 1:
            # Activation memory scales with batch size, so divide by DP size
            for key in adjusted_memory:
                if 'activation_' in key:
                    if f'{key}_per_device' not in adjusted_memory:
                        adjusted_memory[f'{key}_per_device'] = adjusted_memory[key] // dp_size
                    else:
                        adjusted_memory[f'{key}_per_device'] //= dp_size
        
        # Calculate total memory per device
        per_device_keys = [k for k in adjusted_memory.keys() if '_per_device' in k]
        if per_device_keys:
            adjusted_memory['total_per_device'] = sum(adjusted_memory[k] for k in per_device_keys)
        
        # Communication buffer memory (for gradient synchronization)
        if dp_size > 1 or tp_size > 1:
            # Estimate communication buffer as a fraction of parameter memory
            param_memory = adjusted_memory.get('param_parameters', 0)
            comm_buffer_memory = int(param_memory * 0.1)  # 10% of parameter memory
            adjusted_memory['communication_buffers'] = comm_buffer_memory
        
        return adjusted_memory
    
    @staticmethod
    def get_memory_breakdown_summary(memory: Dict[str, int]) -> Dict[str, Any]:
        """
        Get a summary of memory breakdown with percentages and human-readable values.
        
        Args:
            memory: Memory calculations dictionary
            
        Returns:
            Summary with percentages and human-readable values
        """
        total_memory = memory.get('total', 0)
        if total_memory == 0:
            return {'total': 0, 'breakdown': {}}
        
        summary = {
            'total': total_memory,
            'total_human': MemoryCalculator._format_memory(total_memory),
            'breakdown': {}
        }
        
        # Group related memory components
        component_groups = {
            'parameters': ['param_parameters', 'param_gradients', 'param_optimizer_states'],
            'activations': ['activation_input_embeddings', 'activation_layer_activations', 
                          'activation_attention_matrices', 'activation_output_logits'],
            'kv_cache': ['kv_cache_total_kv_cache'],
            'communication': ['communication_buffers']
        }
        
        for group_name, keys in component_groups.items():
            group_memory = sum(memory.get(key, 0) for key in keys)
            if group_memory > 0:
                percentage = (group_memory / total_memory) * 100
                summary['breakdown'][group_name] = {
                    'memory': group_memory,
                    'memory_human': MemoryCalculator._format_memory(group_memory),
                    'percentage': round(percentage, 2)
                }
        
        return summary
    
    @staticmethod
    def estimate_memory_for_sequence_lengths(model_config: Dict[str, Any], total_params: int,
                                           sequence_lengths: list, batch_size: int = 1,
                                           precision: str = 'fp16') -> Dict[int, Dict[str, int]]:
        """
        Estimate memory requirements for different sequence lengths.
        
        Args:
            model_config: Model configuration dictionary
            total_params: Total number of parameters
            sequence_lengths: List of sequence lengths to analyze
            batch_size: Batch size
            precision: Model precision
            
        Returns:
            Dictionary mapping sequence lengths to memory requirements
        """
        results = {}
        
        for seq_len in sequence_lengths:
            memory = MemoryCalculator.compute_total_memory_requirements(
                model_config, total_params, seq_len, batch_size, precision,
                training=False, include_kv_cache=True
            )
            results[seq_len] = memory
        
        return results
    
    @staticmethod
    def _format_memory(memory_bytes: int) -> str:
        """Format memory size in human-readable form."""
        if memory_bytes >= 1024**4:
            return f"{memory_bytes / (1024**4):.2f}TB"
        elif memory_bytes >= 1024**3:
            return f"{memory_bytes / (1024**3):.2f}GB"
        elif memory_bytes >= 1024**2:
            return f"{memory_bytes / (1024**2):.2f}MB"
        elif memory_bytes >= 1024:
            return f"{memory_bytes / 1024:.2f}KB"
        else:
            return f"{memory_bytes}B"