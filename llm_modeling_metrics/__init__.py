"""
LLM Modeling Metrics Package

A comprehensive Python library for analyzing computational requirements 
and performance characteristics of Large Language Models.
"""

__version__ = "1.0.0"
__author__ = "LLM Modeling Metrics Team"

from .core.base_model import BaseModel, ModelMetrics, ParallelConfig
from .core.model_factory import ModelFactory
from .core.config_manager import ConfigManager
from .core.parallel_strategies import ParallelStrategyCalculator

# Import and register model implementations
from .models.dense_model import DenseModel
from .models.moe_model import MoEModel

# Register models with the factory
# ModelFactory.register_model('qwen', DenseModel)
# ModelFactory.register_model('deepseek', MoEModel)
ModelFactory.register_model('dense', DenseModel)
ModelFactory.register_model('moe', MoEModel)  # Generic MoE fallback

__all__ = [
    "BaseModel",
    "ModelMetrics", 
    "ParallelConfig",
    "ModelFactory",
    "ConfigManager",
    "ParallelStrategyCalculator",
    "DenseModel",
    "MoEModel"
]