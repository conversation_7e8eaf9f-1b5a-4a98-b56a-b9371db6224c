"""
Dense model implementation for standard transformer architectures like <PERSON><PERSON><PERSON>, <PERSON><PERSON>, etc.
"""

import math
from typing import Dict, Any, Optional, Tu<PERSON>, List
from ..core.base_model import BaseModel, ParallelConfig
from ..core.operators import (
    AttentionOperator, MLPOperator, LayerNormOperator, MatMulOperator,
    HardwareSpecs, OperatorMetrics
)


class DenseModel(BaseModel):
    """
    Implementation for dense transformer models (non-MoE).
    
    Supports standard transformer architectures like Llama, Qwen, and similar models
    with attention and MLP layers but no mixture of experts.
    """
    
    def _parse_config(self) -> None:
        """Parse the model configuration and extract key parameters."""
        if self.config is None:
            raise ValueError("Model configuration is required")
        
        # Helper function to get config values (handles both dict and object configs)
        def get_config_value(key, default=None):
            if hasattr(self.config, 'get') and callable(getattr(self.config, 'get')):
                return self.config.get(key, default)
            elif hasattr(self.config, key):
                return getattr(self.config, key)
            elif isinstance(self.config, dict):
                return self.config.get(key, default)
            else:
                return default
        
        # Extract common transformer parameters
        self._parsed_config = {
            'hidden_size': get_config_value('hidden_size'),
            'num_hidden_layers': get_config_value('num_hidden_layers'),
            'num_attention_heads': get_config_value('num_attention_heads'),
            'num_key_value_heads': get_config_value('num_key_value_heads', None),
            'intermediate_size': get_config_value('intermediate_size'),
            'vocab_size': get_config_value('vocab_size'),
            'max_position_embeddings': get_config_value('max_position_embeddings'),
            'model_type': get_config_value('model_type', 'unknown'),
            'tie_word_embeddings': get_config_value('tie_word_embeddings', False),
            'rms_norm_eps': get_config_value('rms_norm_eps', 1e-6),
            
            # MLA-specific parameters (for attention mechanism detection)
            'kv_lora_rank': get_config_value('kv_lora_rank'),
            'qk_rope_head_dim': get_config_value('qk_rope_head_dim'),
            'qk_nope_head_dim': get_config_value('qk_nope_head_dim'),
            'v_head_dim': get_config_value('v_head_dim'),
            'q_lora_rank': get_config_value('q_lora_rank'),
            'kv_a_proj_with_mqa': get_config_value('kv_a_proj_with_mqa'),
            'qk_head_dim': get_config_value('qk_head_dim'),
        }
        
        # Handle grouped query attention (GQA)
        if self._parsed_config['num_key_value_heads'] is None:
            self._parsed_config['num_key_value_heads'] = self._parsed_config['num_attention_heads']
        
        print(f'{self._parsed_config=}')
        # Calculate head dimensions
        self._parsed_config['head_dim'] = (
            self._parsed_config['hidden_size'] // self._parsed_config['num_attention_heads']
        )
        
        # Validate configuration
        self._validate_config()
    
    def _validate_config(self) -> None:
        """Validate the parsed configuration."""
        required_params = ['hidden_size', 'num_hidden_layers', 'num_attention_heads', 'vocab_size']
        for param in required_params:
            if self._parsed_config.get(param, 0) <= 0:
                raise ValueError(f"Invalid or missing configuration parameter: {param}")
        
        # Validate head dimensions
        if self._parsed_config['hidden_size'] % self._parsed_config['num_attention_heads'] != 0:
            raise ValueError("hidden_size must be divisible by num_attention_heads")
    
    def compute_attention_params(self) -> int:
        """
        Compute the number of parameters in attention layers using AttentionOperator.

        Returns:
            Number of parameters in all attention layers
        """
        hidden_size = self._parsed_config['hidden_size']
        num_layers = self._parsed_config['num_hidden_layers']
        num_heads = self._parsed_config['num_attention_heads']
        num_kv_heads = self._parsed_config['num_key_value_heads']

        # Create attention operator to compute parameters
        attention_op = AttentionOperator(
            hidden_size=hidden_size,
            num_heads=num_heads,
            num_kv_heads=num_kv_heads,
            precision='fp16',  # Precision doesn't affect parameter count
            batch_size=1,      # Batch size doesn't affect parameter count
            qlen=1             # Sequence length doesn't affect parameter count
        )

        params_per_layer = attention_op.compute_params()
        return params_per_layer * num_layers
    
    def compute_mlp_params(self) -> int:
        """
        Compute the number of parameters in MLP/feed-forward layers using MLPOperator.

        Returns:
            Number of parameters in all MLP layers
        """
        hidden_size = self._parsed_config['hidden_size']
        intermediate_size = self._parsed_config['intermediate_size']
        num_layers = self._parsed_config['num_hidden_layers']

        # Create MLP operator to compute parameters
        mlp_op = MLPOperator(
            hidden_size=hidden_size,
            intermediate_size=intermediate_size,
            precision='fp16',  # Precision doesn't affect parameter count
            batch_size=1,      # Batch size doesn't affect parameter count
            qlen=1             # Sequence length doesn't affect parameter count
        )

        params_per_layer = mlp_op.compute_params()
        return params_per_layer * num_layers
    
    def compute_embedding_params(self) -> int:
        """
        Compute the number of parameters in embedding layers.
        
        Includes:
        - Token embeddings: vocab_size * hidden_size
        - Output embeddings (if not tied): vocab_size * hidden_size
        
        Returns:
            Number of parameters in embedding layers
        """
        vocab_size = self._parsed_config['vocab_size']
        hidden_size = self._parsed_config['hidden_size']
        tie_embeddings = self._parsed_config['tie_word_embeddings']
        
        # Token embeddings
        token_embedding_params = vocab_size * hidden_size
        
        # Output embeddings (language modeling head)
        if tie_embeddings:
            # Embeddings are shared, no additional parameters
            output_embedding_params = 0
        else:
            # Separate output embedding layer
            output_embedding_params = vocab_size * hidden_size
        
        return token_embedding_params + output_embedding_params
    
    def compute_flops(self, sequence_length: int = 2048, batch_size: int = 1) -> Dict[str, int]:
        """
        Compute FLOPs for forward pass using operators.

        Args:
            sequence_length: Input sequence length
            batch_size: Batch size

        Returns:
            Dictionary with FLOP breakdown by component
        """
        hidden_size = self._parsed_config['hidden_size']
        num_layers = self._parsed_config['num_hidden_layers']
        num_heads = self._parsed_config['num_attention_heads']
        num_kv_heads = self._parsed_config['num_key_value_heads']
        intermediate_size = self._parsed_config['intermediate_size']
        vocab_size = self._parsed_config['vocab_size']

        flops = {}

        # Embedding lookup (no FLOPs, just memory access)
        flops['embeddings'] = 0

        # Attention FLOPs using AttentionOperator
        attention_op = AttentionOperator(
            hidden_size=hidden_size,
            num_heads=num_heads,
            num_kv_heads=num_kv_heads,
            precision='fp16',
            batch_size=batch_size,
            qlen=sequence_length
        )
        attention_flops_per_layer = attention_op.compute_flops(
            batch_size=batch_size, qlen=sequence_length, kv_lens=sequence_length
        )
        flops['attention'] = attention_flops_per_layer * num_layers

        # MLP FLOPs using MLPOperator
        mlp_op = MLPOperator(
            hidden_size=hidden_size,
            intermediate_size=intermediate_size,
            precision='fp16',
            batch_size=batch_size,
            qlen=sequence_length
        )
        mlp_flops_per_layer = mlp_op.compute_flops(
            batch_size=batch_size, qlen=sequence_length
        )
        flops['mlp'] = mlp_flops_per_layer * num_layers

        # Layer norm FLOPs using LayerNormOperator
        layernorm_op = LayerNormOperator(hidden_size=hidden_size, precision='fp16')
        layernorm_flops_per_layer = layernorm_op.compute_flops(
            batch_size=batch_size, sequence_length=sequence_length
        )
        # 2 layer norms per transformer layer (pre-attention and pre-MLP)
        flops['layernorm'] = layernorm_flops_per_layer * 2 * num_layers

        # Final layer norm
        flops['final_layernorm'] = layernorm_flops_per_layer

        # Language modeling head (if not tied) using MatMulOperator
        if not self._parsed_config['tie_word_embeddings']:
            lm_head_op = MatMulOperator(
                M=batch_size * sequence_length,
                N=vocab_size,
                K=hidden_size,
                precision='fp16'
            )
            flops['lm_head'] = lm_head_op.compute_flops()
        else:
            flops['lm_head'] = 0

        return flops
    
    def compute_memory_requirements(self, sequence_length: int = 2048, 
                                  batch_size: int = 1, dtype: str = 'fp16',
                                  weight_dtype: str = None,
                                  activation_dtype: str = None,
                                  grad_dtype: str = None,
                                  optimizer_dtype: str = None,
                                  kv_cache_dtype: str = None,
                                  training: bool = False,
                                  include_kv_cache: bool = False) -> Dict[str, int]:
        """
        Compute memory requirements for the model with mixed precision support.
        
        Args:
            sequence_length: Input sequence length
            batch_size: Batch size
            dtype: Default data type for calculations (backward compatibility)
            weight_dtype: Data type for model weights (overrides dtype if provided)
            activation_dtype: Data type for activations (overrides dtype if provided)
            grad_dtype: Data type for gradients (overrides dtype if provided)
            optimizer_dtype: Data type for optimizer states (overrides dtype if provided)
            kv_cache_dtype: Data type for KV cache (overrides dtype if provided)
            training: Whether this is for training (includes gradients/optimizer)
            include_kv_cache: Whether to include KV cache memory
            
        Returns:
            Dictionary with memory breakdown by component (in bytes)
        """
        from ..metrics.memory_calculator import MemoryCalculator
        
        # Use provided dtypes or fall back to default dtype for backward compatibility
        effective_weight_dtype = weight_dtype or dtype
        effective_activation_dtype = activation_dtype or dtype
        effective_grad_dtype = grad_dtype or dtype
        effective_optimizer_dtype = optimizer_dtype or 'fp32'  # Optimizer typically uses fp32
        effective_kv_cache_dtype = kv_cache_dtype or dtype
        
        total_params = self.get_total_params()
        config_dict = self._parsed_config
        
        # Use mixed precision memory calculation if any specific dtypes are provided
        if any([weight_dtype, activation_dtype, grad_dtype, optimizer_dtype, kv_cache_dtype]):
            detailed_memory = MemoryCalculator.compute_mixed_precision_total_memory(
                config_dict,
                total_params,
                sequence_length=sequence_length,
                batch_size=batch_size,
                weight_dtype=effective_weight_dtype,
                activation_dtype=effective_activation_dtype,
                grad_dtype=effective_grad_dtype,
                optimizer_dtype=effective_optimizer_dtype,
                kv_cache_dtype=effective_kv_cache_dtype,
                training=training,
                activation_checkpointing=False,
                include_kv_cache=include_kv_cache
            )
        else:
            # Fall back to original method for backward compatibility
            detailed_memory = MemoryCalculator.compute_total_memory_requirements(
                config_dict,
                total_params,
                sequence_length=sequence_length,
                batch_size=batch_size,
                precision=dtype,
                training=training,
                activation_checkpointing=False,
                include_kv_cache=include_kv_cache
            )
        
        # Transform to expected structure for backward compatibility
        param_memory = detailed_memory.get('param_parameters', 0)
        memory = {
            'parameters': param_memory,
            'activations': detailed_memory.get('activation_total', 0),
            'total': detailed_memory.get('total', 0),
            'dtype': effective_weight_dtype if any([weight_dtype, activation_dtype, grad_dtype, optimizer_dtype, kv_cache_dtype]) else dtype,
            'bytes_per_element': MemoryCalculator.PRECISION_BYTES.get(effective_weight_dtype if any([weight_dtype, activation_dtype, grad_dtype, optimizer_dtype, kv_cache_dtype]) else dtype, 2)
        }
        
        # Add training-specific memory components
        if training:
            memory['gradients'] = detailed_memory.get('param_gradients', param_memory)
            memory['optimizer_states'] = detailed_memory.get('param_optimizer_states', 2 * param_memory)
        
        # Add KV cache memory if included
        if include_kv_cache:
            memory['kv_cache'] = detailed_memory.get('kv_cache_total', 0)
        
        # Add detailed breakdown for enhanced functionality
        memory.update({
            'input_embeddings': detailed_memory.get('activation_input_embeddings', 0),
            'layer_activations': detailed_memory.get('activation_layer_activations', 0),
            'attention_matrices': detailed_memory.get('activation_attention_matrices', 0),
            'output_logits': detailed_memory.get('activation_output_logits', 0)
        })
        
        # Add dtype information for mixed precision
        if 'dtypes' in detailed_memory:
            memory['dtypes'] = detailed_memory['dtypes']
        
        return memory
    
    def get_matrix_shapes(self, parallel_config: Optional[ParallelConfig] = None,
                         sequence_length: int = 2048, batch_size: int = 1,
                         detailed: bool = False) -> Dict[str, Any]:
        """
        Get matrix shapes for model operations under parallel configuration.
        
        Args:
            parallel_config: Parallel execution configuration
            sequence_length: Input sequence length for activation shapes
            batch_size: Batch size for activation shapes
            detailed: Whether to return detailed shape analysis
            
        Returns:
            Dictionary with matrix shapes for different operations
        """
        if detailed:
            # Use ShapeAnalyzer for comprehensive analysis
            from ..metrics.shape_analyzer import ShapeAnalyzer
            return ShapeAnalyzer.get_detailed_shape_analysis(
                self._parsed_config, sequence_length, batch_size, parallel_config
            )
        
        hidden_size = self._parsed_config['hidden_size']
        num_heads = self._parsed_config['num_attention_heads']
        num_kv_heads = self._parsed_config['num_key_value_heads']
        head_dim = self._parsed_config['head_dim']
        intermediate_size = self._parsed_config['intermediate_size']
        vocab_size = self._parsed_config['vocab_size']
        
        # Default shapes (no parallelism)
        shapes = {
            'attention': {
                'q_proj': (hidden_size, num_heads * head_dim),
                'k_proj': (hidden_size, num_kv_heads * head_dim),
                'v_proj': (hidden_size, num_kv_heads * head_dim),
                'o_proj': (num_heads * head_dim, hidden_size),
            },
            'mlp': {
                'gate_proj': (hidden_size, intermediate_size),
                'up_proj': (hidden_size, intermediate_size),
                'down_proj': (intermediate_size, hidden_size),
            },
            'embeddings': {
                'token_embeddings': (vocab_size, hidden_size),
            }
        }
        
        # Add output embeddings if not tied
        if not self._parsed_config['tie_word_embeddings']:
            shapes['embeddings']['lm_head'] = (hidden_size, vocab_size)
        
        # Apply tensor parallelism if specified
        if parallel_config and parallel_config.tensor_parallel_size > 1:
            shapes = self._apply_tensor_parallelism(shapes, parallel_config.tensor_parallel_size)
        
        return shapes
    
    def _apply_tensor_parallelism(self, shapes: Dict[str, Any], tp_size: int) -> Dict[str, Any]:
        """
        Apply tensor parallelism to matrix shapes.
        
        Args:
            shapes: Original matrix shapes
            tp_size: Tensor parallel size
            
        Returns:
            Modified shapes under tensor parallelism
        """
        parallel_shapes = {}
        
        # Attention shapes under tensor parallelism
        attention_shapes = shapes['attention'].copy()
        
        # Q, K, V projections are column-parallel (split output dimension)
        q_shape = attention_shapes['q_proj']
        attention_shapes['q_proj'] = (q_shape[0], q_shape[1] // tp_size)
        
        k_shape = attention_shapes['k_proj']
        attention_shapes['k_proj'] = (k_shape[0], k_shape[1] // tp_size)
        
        v_shape = attention_shapes['v_proj']
        attention_shapes['v_proj'] = (v_shape[0], v_shape[1] // tp_size)
        
        # Output projection is row-parallel (split input dimension)
        o_shape = attention_shapes['o_proj']
        attention_shapes['o_proj'] = (o_shape[0] // tp_size, o_shape[1])
        
        parallel_shapes['attention'] = attention_shapes
        
        # MLP shapes under tensor parallelism
        mlp_shapes = shapes['mlp'].copy()
        
        # Gate and up projections are column-parallel
        gate_shape = mlp_shapes['gate_proj']
        mlp_shapes['gate_proj'] = (gate_shape[0], gate_shape[1] // tp_size)
        
        up_shape = mlp_shapes['up_proj']
        mlp_shapes['up_proj'] = (up_shape[0], up_shape[1] // tp_size)
        
        # Down projection is row-parallel
        down_shape = mlp_shapes['down_proj']
        mlp_shapes['down_proj'] = (down_shape[0] // tp_size, down_shape[1])
        
        parallel_shapes['mlp'] = mlp_shapes
        
        # Embeddings remain the same (replicated across devices)
        parallel_shapes['embeddings'] = shapes['embeddings'].copy()
        
        return parallel_shapes
    
    def validate_parallel_config(self, parallel_config: ParallelConfig) -> bool:
        """
        Validate that a parallel configuration is feasible for this dense model.
        
        Args:
            parallel_config: Parallel configuration to validate
            
        Returns:
            True if configuration is valid, False otherwise
        """
        if not super().validate_parallel_config(parallel_config):
            return False
        
        tp_size = parallel_config.tensor_parallel_size
        
        # Check if attention dimensions are divisible by tensor parallel size
        num_heads = self._parsed_config['num_attention_heads']
        num_kv_heads = self._parsed_config['num_key_value_heads']
        intermediate_size = self._parsed_config['intermediate_size']
        
        if num_heads % tp_size != 0:
            return False
        if num_kv_heads % tp_size != 0:
            return False
        if intermediate_size % tp_size != 0:
            return False
        
        return True
    
    def get_parallel_validation_errors(self, parallel_config: ParallelConfig) -> List[str]:
        """
        Get detailed validation errors for a parallel configuration.
        
        Args:
            parallel_config: Parallel configuration to validate
            
        Returns:
            List of validation error messages
        """
        from ..metrics.shape_analyzer import ShapeAnalyzer
        
        errors = []
        
        # Get base shapes
        shapes = self.get_matrix_shapes()
        
        # Validate tensor parallelism
        if parallel_config.tensor_parallel_size > 1:
            attention_errors = ShapeAnalyzer.validate_tensor_parallel_shapes(
                shapes['attention'], parallel_config.tensor_parallel_size
            )
            mlp_errors = ShapeAnalyzer.validate_tensor_parallel_shapes(
                shapes['mlp'], parallel_config.tensor_parallel_size
            )
            errors.extend(attention_errors)
            errors.extend(mlp_errors)
        
        return errors
    
    def get_communication_analysis(self, parallel_config: ParallelConfig,
                                 sequence_length: int = 2048, 
                                 batch_size: int = 1) -> Dict[str, Any]:
        """
        Get communication volume analysis for parallel execution.
        
        Args:
            parallel_config: Parallel configuration
            sequence_length: Input sequence length
            batch_size: Batch size
            
        Returns:
            Dictionary with communication analysis
        """
        from ..metrics.shape_analyzer import ShapeAnalyzer
        
        # Get detailed shapes with activations
        shapes = self.get_matrix_shapes(
            parallel_config, sequence_length, batch_size, detailed=True
        )
        
        if 'communication' in shapes:
            return shapes['communication']
        else:
            # Fallback for basic analysis
            attention_shapes = ShapeAnalyzer.compute_attention_shapes(
                self._parsed_config['hidden_size'],
                self._parsed_config['num_attention_heads'],
                self._parsed_config['num_key_value_heads'],
                self._parsed_config['head_dim'],
                sequence_length, batch_size
            )
            
            mlp_shapes = ShapeAnalyzer.compute_mlp_shapes(
                self._parsed_config['hidden_size'],
                self._parsed_config['intermediate_size'],
                sequence_length, batch_size
            )
            
            return {
                'attention': ShapeAnalyzer.compute_communication_volume(
                    attention_shapes, parallel_config
                ),
                'mlp': ShapeAnalyzer.compute_communication_volume(
                    mlp_shapes, parallel_config
                )
            }
    
    def get_attention_mechanism_type(self) -> str:
        """
        Return the attention mechanism type for this dense model.
        
        Implements attention mechanism detection logic specific to dense models.
        
        Returns:
            String indicating attention mechanism type ('MHA', 'GQA', 'MLA', 'Unknown')
        """
        from ..metrics.memory_calculator import MemoryCalculator
        
        # Use the parsed config for more reliable detection
        attention_info = MemoryCalculator.get_attention_mechanism_info(self._parsed_config)
        
        return attention_info['type']
    
    def get_memory_breakdown_by_dtype(self, sequence_length: int = 2048,
                                     batch_size: int = 1, dtype: str = 'fp16',
                                     include_kv_cache: bool = True) -> Dict[str, Any]:
        """
        Get detailed memory breakdown with dtype-specific calculations for dense models.
        
        Args:
            sequence_length: Input sequence length
            batch_size: Batch size
            dtype: Data type for calculations ('fp16', 'bf16', 'fp32', 'int8')
            include_kv_cache: Whether to include KV cache memory
            
        Returns:
            Dictionary with memory breakdown for API response
        """
        # Get base memory breakdown
        result = super().get_memory_breakdown_by_dtype(
            sequence_length, batch_size, dtype, include_kv_cache
        )
        
        return result
    
    def compute_total_params(self) -> int:
        """
        Compute total number of parameters in the dense model.
        
        Returns:
            Total parameter count
        """
        return (self.compute_attention_params() + 
                self.compute_mlp_params() + 
                self.compute_embedding_params())
    
    def get_operator_breakdown(self, batch_size: int = 1, sequence_length: int = 2048,
                              hardware_name: str = 'nvidia_h100_sxm5') -> Dict[str, Any]:
        """
        Get detailed operator-level breakdown similar to Table 2 in the paper.
        
        Args:
            batch_size: Batch size for computation
            sequence_length: Sequence length
            hardware_name: Hardware specification to use for performance modeling
            
        Returns:
            Dictionary with operator-level analysis
        """
        try:
            hardware = HardwareSpecs.from_gpu_spec(hardware_name)
        except (FileNotFoundError, ValueError):
            # Fallback to default specs if GPU specs file not found
            hardware = HardwareSpecs(
                name="Default GPU",
                peak_flops={'fp16': 100.0, 'bf16': 100.0, 'fp32': 50.0},
                memory_bandwidth_gbps=1000.0,
                memory_size_gb=40
            )
        
        hidden_size = self._parsed_config['hidden_size']
        num_heads = self._parsed_config['num_attention_heads']
        num_kv_heads = self._parsed_config['num_key_value_heads']
        intermediate_size = self._parsed_config['intermediate_size']
        num_layers = self._parsed_config['num_hidden_layers']
        vocab_size = self._parsed_config['vocab_size']
        
        operators = {}
        
        # Attention operator
        attention_op = AttentionOperator(
            hidden_size=hidden_size,
            num_heads=num_heads,
            num_kv_heads=num_kv_heads
        )
        attention_metrics = attention_op.compute_metrics(
            hardware, batch_size=batch_size, sequence_length=sequence_length
        )
        operators['attention'] = {
            'operator': attention_op,
            'metrics_per_layer': attention_metrics,
            'metrics_total': OperatorMetrics(
                flops=attention_metrics.flops * num_layers,
                memory_capacity_bytes=attention_metrics.memory_capacity_bytes * num_layers,
                memory_movement_bytes=attention_metrics.memory_movement_bytes * num_layers,
                execution_time_ms=attention_metrics.execution_time_ms * num_layers,
                arithmetic_intensity=attention_metrics.arithmetic_intensity,
                utilization=attention_metrics.utilization
            )
        }
        
        # MLP operator
        mlp_op = MLPOperator(
            hidden_size=hidden_size,
            intermediate_size=intermediate_size
        )
        mlp_metrics = mlp_op.compute_metrics(
            hardware, batch_size=batch_size, sequence_length=sequence_length
        )
        operators['mlp'] = {
            'operator': mlp_op,
            'metrics_per_layer': mlp_metrics,
            'metrics_total': OperatorMetrics(
                flops=mlp_metrics.flops * num_layers,
                memory_capacity_bytes=mlp_metrics.memory_capacity_bytes * num_layers,
                memory_movement_bytes=mlp_metrics.memory_movement_bytes * num_layers,
                execution_time_ms=mlp_metrics.execution_time_ms * num_layers,
                arithmetic_intensity=mlp_metrics.arithmetic_intensity,
                utilization=mlp_metrics.utilization
            )
        }
        
        # Layer normalization (2 per layer: pre-attention and pre-MLP)
        layernorm_op = LayerNormOperator(hidden_size=hidden_size)
        layernorm_metrics = layernorm_op.compute_metrics(
            hardware, batch_size=batch_size, sequence_length=sequence_length
        )
        operators['layernorm'] = {
            'operator': layernorm_op,
            'metrics_per_layer': OperatorMetrics(
                flops=layernorm_metrics.flops * 2,  # 2 per layer
                memory_capacity_bytes=layernorm_metrics.memory_capacity_bytes * 2,
                memory_movement_bytes=layernorm_metrics.memory_movement_bytes * 2,
                execution_time_ms=layernorm_metrics.execution_time_ms * 2,
                arithmetic_intensity=layernorm_metrics.arithmetic_intensity,
                utilization=layernorm_metrics.utilization
            ),
            'metrics_total': OperatorMetrics(
                flops=layernorm_metrics.flops * 2 * num_layers,
                memory_capacity_bytes=layernorm_metrics.memory_capacity_bytes * 2 * num_layers,
                memory_movement_bytes=layernorm_metrics.memory_movement_bytes * 2 * num_layers,
                execution_time_ms=layernorm_metrics.execution_time_ms * 2 * num_layers,
                arithmetic_intensity=layernorm_metrics.arithmetic_intensity,
                utilization=layernorm_metrics.utilization
            )
        }
        
        # Embedding layers
        token_embedding_op = MatMulOperator(vocab_size, hidden_size)
        embedding_metrics = token_embedding_op.compute_metrics(
            hardware, batch_size=batch_size, sequence_length=sequence_length
        )
        operators['embeddings'] = {
            'operator': token_embedding_op,
            'metrics_total': embedding_metrics
        }
        
        # Language modeling head (if not tied)
        if not self._parsed_config['tie_word_embeddings']:
            lm_head_op = MatMulOperator(hidden_size, vocab_size)
            lm_head_metrics = lm_head_op.compute_metrics(
                hardware, batch_size=batch_size, sequence_length=sequence_length
            )
            operators['lm_head'] = {
                'operator': lm_head_op,
                'metrics_total': lm_head_metrics
            }
        
        # Compute totals
        total_flops = sum(op_data['metrics_total'].flops for op_data in operators.values())
        total_memory_capacity = sum(op_data['metrics_total'].memory_capacity_bytes for op_data in operators.values())
        total_memory_movement = sum(op_data['metrics_total'].memory_movement_bytes for op_data in operators.values())
        total_time = sum(op_data['metrics_total'].execution_time_ms for op_data in operators.values())
        
        return {
            'hardware': hardware,
            'operators': operators,
            'totals': {
                'flops': total_flops,
                'memory_capacity_bytes': total_memory_capacity,
                'memory_movement_bytes': total_memory_movement,
                'execution_time_ms': total_time,
                'arithmetic_intensity': total_flops / total_memory_movement if total_memory_movement > 0 else 0,
                'throughput_tokens_per_sec': (batch_size * sequence_length * 1000) / total_time if total_time > 0 else 0
            },
            'model_config': {
                'hidden_size': hidden_size,
                'num_layers': num_layers,
                'num_heads': num_heads,
                'num_kv_heads': num_kv_heads,
                'intermediate_size': intermediate_size,
                'vocab_size': vocab_size,
                'batch_size': batch_size,
                'sequence_length': sequence_length
            }
        }
    
    def get_roofline_analysis(self, batch_size: int = 1, sequence_length: int = 2048,
                             hardware_name: str = 'nvidia_h100_sxm5') -> Dict[str, Any]:
        """
        Get roofline analysis for the model on specified hardware.
        
        Args:
            batch_size: Batch size
            sequence_length: Sequence length  
            hardware_name: Hardware specification
            
        Returns:
            Dictionary with roofline analysis data
        """
        operator_breakdown = self.get_operator_breakdown(batch_size, sequence_length, hardware_name)
        hardware = operator_breakdown['hardware']
        
        # Get peak performance for different precisions
        peak_flops = hardware.peak_flops
        memory_bandwidth_gbps = hardware.memory_bandwidth_gbps
        
        roofline_data = {
            'hardware': {
                'name': hardware.name,
                'peak_flops': peak_flops,
                'memory_bandwidth_gbps': memory_bandwidth_gbps
            },
            'operators': {},
            'model_point': {}
        }
        
        # Analyze each operator
        for op_name, op_data in operator_breakdown['operators'].items():
            metrics = op_data['metrics_total']
            
            # Arithmetic intensity (FLOPs per byte)
            ai = metrics.arithmetic_intensity
            
            # Achieved performance (GFLOPS)
            achieved_gflops = metrics.flops / (metrics.execution_time_ms / 1000) / 1e9 if metrics.execution_time_ms > 0 else 0
            
            # Memory bandwidth utilization
            memory_bw_utilization = (metrics.memory_movement_bytes / (metrics.execution_time_ms / 1000)) / (memory_bandwidth_gbps * 1e9) * 100 if metrics.execution_time_ms > 0 else 0
            
            roofline_data['operators'][op_name] = {
                'arithmetic_intensity': ai,
                'achieved_gflops': achieved_gflops,
                'memory_bandwidth_utilization': memory_bw_utilization,
                'compute_utilization': metrics.utilization,
                'is_compute_bound': ai > (peak_flops.get('fp16', 100) * 1e12) / (memory_bandwidth_gbps * 1e9),
                'bottleneck': 'compute' if ai > (peak_flops.get('fp16', 100) * 1e12) / (memory_bandwidth_gbps * 1e9) else 'memory'
            }
        
        # Overall model characteristics
        totals = operator_breakdown['totals']
        model_ai = totals['arithmetic_intensity']
        model_gflops = totals['flops'] / (totals['execution_time_ms'] / 1000) / 1e9 if totals['execution_time_ms'] > 0 else 0
        
        roofline_data['model_point'] = {
            'arithmetic_intensity': model_ai,
            'achieved_gflops': model_gflops,
            'peak_gflops': peak_flops.get('fp16', 100) * 1000,  # Convert TFLOPS to GFLOPS
            'efficiency': (model_gflops / (peak_flops.get('fp16', 100) * 1000)) * 100 if peak_flops.get('fp16', 100) > 0 else 0,
            'is_compute_bound': model_ai > (peak_flops.get('fp16', 100) * 1e12) / (memory_bandwidth_gbps * 1e9),
            'bottleneck': 'compute' if model_ai > (peak_flops.get('fp16', 100) * 1e12) / (memory_bandwidth_gbps * 1e9) else 'memory'
        }
        
        return roofline_data