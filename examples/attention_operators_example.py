#!/usr/bin/env python3
"""
Example demonstrating different attention operators for various attention mechanisms.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm_modeling_metrics.core.operators import AttentionOperator, MLAAttentionOperator

def compare_attention_mechanisms():
    """Compare different attention mechanisms using the operator classes."""
    
    # Common parameters
    hidden_size = 4096
    num_heads = 32
    batch_size = 1
    seq_len = 1  # Decode phase
    kv_len = 2048  # Context length
    
    print("=== Attention Mechanism Comparison ===")
    print(f"Model: {hidden_size}D hidden, {num_heads} heads")
    print(f"Scenario: Decode phase (B={batch_size}, S={seq_len}, KV_len={kv_len})")
    print()
    
    # 1. Multi-Head Attention (MHA)
    mha_operator = AttentionOperator(
        hidden_size=hidden_size,
        num_heads=num_heads,
        num_kv_heads=num_heads,  # Same as num_heads for MHA
        precision='fp16',
        batch_size=batch_size,
        qlen=seq_len
    )
    
    # 2. Grouped Query Attention (GQA) - 8 KV heads
    gqa_operator = AttentionOperator(
        hidden_size=hidden_size,
        num_heads=num_heads,
        num_kv_heads=8,  # Fewer KV heads for GQA
        precision='fp16',
        batch_size=batch_size,
        qlen=seq_len
    )
    
    # 3. Multi-head Latent Attention (MLA) - DeepSeek style
    mla_operator = MLAAttentionOperator(
        hidden_size=hidden_size,
        num_heads=num_heads,
        kv_lora_rank=256,  # Compressed KV representation
        q_lora_rank=768,   # Query LoRA rank
        qk_rope_head_dim=64,
        qk_nope_head_dim=64,
        v_head_dim=128,
        precision='fp16',
        batch_size=batch_size,
        qlen=seq_len
    )
    
    # Compare FLOPs
    print("=== FLOP Comparison ===")
    mha_flops = mha_operator.compute_flops(kv_lens=kv_len)
    gqa_flops = gqa_operator.compute_flops(kv_lens=kv_len)
    mla_flops = mla_operator.compute_flops(kv_lens=kv_len)
    
    print(f"{'Mechanism':<20} {'FLOPs (GFLOPs)':<15} {'Relative':<10} {'Description'}")
    print("-" * 75)
    print(f"{'MHA':<20} {mha_flops/1e9:<15.3f} {1.0:<10.2f} Standard multi-head attention")
    print(f"{'GQA (8 KV heads)':<20} {gqa_flops/1e9:<15.3f} {gqa_flops/mha_flops:<10.2f} Grouped query attention")
    print(f"{'MLA':<20} {mla_flops/1e9:<15.3f} {mla_flops/mha_flops:<10.2f} Multi-head latent attention")
    print()
    
    # Compare Memory
    print("=== Memory Comparison ===")
    mha_memory = mha_operator.compute_memory_capacity_bytes(kv_lens=kv_len)
    gqa_memory = gqa_operator.compute_memory_capacity_bytes(kv_lens=kv_len)
    mla_memory = mla_operator.compute_memory_capacity_bytes(kv_lens=kv_len)
    
    print(f"{'Mechanism':<20} {'Memory (MB)':<15} {'Relative':<10} {'Key Feature'}")
    print("-" * 75)
    print(f"{'MHA':<20} {mha_memory/1e6:<15.2f} {1.0:<10.2f} Full KV cache")
    print(f"{'GQA (8 KV heads)':<20} {gqa_memory/1e6:<15.2f} {gqa_memory/mha_memory:<10.2f} Reduced KV heads")
    print(f"{'MLA':<20} {mla_memory/1e6:<15.2f} {mla_memory/mha_memory:<10.2f} Compressed KV cache")
    print()
    
    # MLA-specific breakdown
    print("=== MLA Detailed Breakdown ===")
    mla_linear_flops = mla_operator.compute_linear_projection_flops()
    mla_attention_flops = mla_operator.compute_attention_computation_flops(kv_lens=kv_len)
    
    print(f"Linear projections: {mla_linear_flops/1e9:.3f} GFLOPs")
    print(f"Attention computation: {mla_attention_flops/1e9:.3f} GFLOPs")
    print(f"Total: {(mla_linear_flops + mla_attention_flops)/1e9:.3f} GFLOPs")
    print()
    
    # Show detailed projection breakdown using MatMul operators
    print("=== MLA Projection Breakdown (MatMul Operators) ===")
    projection_breakdown = mla_operator.get_projection_breakdown()
    
    print(f"{'Projection':<15} {'FLOPs (MFLOPs)':<15} {'Params (M)':<12} {'Description'}")
    print("-" * 70)
    
    for name, info in projection_breakdown.items():
        flops_mflops = info['flops'] / 1e6
        params_m = info['weight_params'] / 1e6
        print(f"{name:<15} {flops_mflops:<15.2f} {params_m:<12.2f} {info['description']}")
    
    print()
    print("Benefits of using MatMul operators:")
    print("• Consistent calculations across all linear operations")
    print("• Easy to analyze individual projection components")
    print("• Reusable for different batch sizes and sequence lengths")
    print()
    
    # Show all MatMul shapes
    print("=== MatMul Shape Inspection ===")
    from llm_modeling_metrics.core.operators import print_matmul_shapes
    
    operators = [mha_operator, gqa_operator, mla_operator]
    print_matmul_shapes(operators, "Attention Mechanisms MatMul Shapes")
    print()
    
    # Usage recommendations
    print("=== Usage Recommendations ===")
    print("• MHA: Best for small models or when memory is not a constraint")
    print("• GQA: Good balance between performance and memory efficiency")
    print("• MLA: Best for large models where KV cache memory is critical")
    print("• All operators support different precisions (fp16, bf16, fp32)")
    print()
    
    # Extension examples
    print("=== Extension Examples ===")
    print("To add new attention mechanisms:")
    print("1. Inherit from BaseOperator")
    print("2. Implement compute_flops(), compute_memory_capacity_bytes(), compute_memory_movement_bytes()")
    print("3. Add mechanism-specific parameters and methods")
    print()
    print("Example mechanisms to implement:")
    print("• FlashAttention: Memory-efficient attention with tiling")
    print("• PagedAttention: Dynamic memory allocation for KV cache")
    print("• Sliding Window Attention: Local attention patterns")
    print("• Sparse Attention: Attention with sparse patterns")

def demonstrate_scaling():
    """Demonstrate how attention mechanisms scale with model size."""
    
    print("=== Scaling Analysis ===")
    
    # Different model sizes
    model_configs = [
        {"name": "Small (1B)", "hidden_size": 2048, "num_heads": 16},
        {"name": "Medium (7B)", "hidden_size": 4096, "num_heads": 32},
        {"name": "Large (70B)", "hidden_size": 8192, "num_heads": 64},
        {"name": "XL (671B)", "hidden_size": 7168, "num_heads": 128},  # DeepSeek V3
    ]
    
    print(f"{'Model Size':<15} {'MHA Memory (GB)':<15} {'GQA Memory (GB)':<15} {'MLA Memory (GB)':<15}")
    print("-" * 70)
    
    for config in model_configs:
        hidden_size = config["hidden_size"]
        num_heads = config["num_heads"]
        
        # Create operators
        mha = AttentionOperator(hidden_size, num_heads, num_heads, precision='fp16', batch_size=1, qlen=1)
        gqa = AttentionOperator(hidden_size, num_heads, 8, precision='fp16', batch_size=1, qlen=1)  # 8 KV heads
        mla = MLAAttentionOperator(
            hidden_size, num_heads, 
            kv_lora_rank=min(512, hidden_size//8),  # Scale with model size
            precision='fp16',
            batch_size=1, qlen=1
        )
        
        # Compute memory for long context (8K)
        kv_len = 8192
        mha_mem = mha.compute_memory_capacity_bytes(kv_lens=kv_len) / 1e9
        gqa_mem = gqa.compute_memory_capacity_bytes(kv_lens=kv_len) / 1e9
        mla_mem = mla.compute_memory_capacity_bytes(kv_lens=kv_len) / 1e9
        
        print(f"{config['name']:<15} {mha_mem:<15.2f} {gqa_mem:<15.2f} {mla_mem:<15.2f}")
    
    print()
    print("Key Observations:")
    print("• MLA provides massive memory savings for large models")
    print("• GQA offers a good middle ground for most use cases")
    print("• Memory savings become more significant with larger models")

if __name__ == "__main__":
    compare_attention_mechanisms()
    print()
    demonstrate_scaling()