#!/usr/bin/env python3
"""
Example script demonstrating operator-based modeling similar to Table 2 in the paper.
This shows how to analyze LLM models at the operator level for performance modeling.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from llm_modeling_metrics.models.dense_model import DenseModel
from llm_modeling_metrics.models.moe_model import MoEModel
from llm_modeling_metrics.core.base_model import ParallelConfig
from llm_modeling_metrics.utils.table_generator import (
    generate_operator_table, generate_roofline_table, 
    compare_models_table, generate_paper_style_table
)


def create_llama_config():
    """Create a Llama-style model configuration."""
    return {
        'hidden_size': 4096,
        'num_hidden_layers': 32,
        'num_attention_heads': 32,
        'num_key_value_heads': 32,
        'intermediate_size': 11008,
        'vocab_size': 32000,
        'max_position_embeddings': 2048,
        'model_type': 'llama',
        'tie_word_embeddings': False,
        'rms_norm_eps': 1e-6
    }


def create_deepseek_moe_config():
    """Create a DeepSeek MoE-style model configuration."""
    return {
        'hidden_size': 4096,
        'num_hidden_layers': 27,
        'num_attention_heads': 32,
        'num_key_value_heads': 32,
        'intermediate_size': 11008,
        'moe_intermediate_size': 1407,
        'vocab_size': 102400,
        'max_position_embeddings': 4096,
        'model_type': 'deepseek',
        'tie_word_embeddings': False,
        'rms_norm_eps': 1e-6,
        
        # MoE-specific parameters
        'n_shared_experts': 2,
        'n_routed_experts': 64,
        'num_experts_per_tok': 6,
        'moe_layer_freq': 1,
        'first_k_dense_replace': 1,
        'routed_scaling_factor': 1.0,
        
        # DeepSeek MLA parameters
        'kv_lora_rank': 512,
        'q_lora_rank': 1536,
        'qk_rope_head_dim': 64,
        'v_head_dim': 128,
        'qk_nope_head_dim': 128,
    }


def main():
    print("=== LLM Operator-Based Modeling Example ===\n")
    
    # Create model instances
    print("Creating model instances...")
    llama_config = create_llama_config()
    deepseek_config = create_deepseek_moe_config()
    
    llama_model = DenseModel("llama-7b", llama_config)
    deepseek_model = MoEModel("deepseek-moe", deepseek_config)
    
    # Analysis parameters
    batch_size = 1
    sequence_length = 2048
    hardware_name = 'nvidia_h100_sxm5'
    
    print(f"Analysis parameters:")
    print(f"  Batch size: {batch_size}")
    print(f"  Sequence length: {sequence_length}")
    print(f"  Hardware: {hardware_name}")
    print()
    
    # 1. Dense Model Operator Analysis
    print("=== Dense Model (Llama-style) Operator Analysis ===")
    try:
        llama_table = generate_operator_table(
            llama_model, batch_size, sequence_length, hardware_name, format_type='pandas'
        )
        print(llama_table.to_string(index=False))
        print()
        
        # Roofline analysis
        print("=== Dense Model Roofline Analysis ===")
        llama_roofline = generate_roofline_table(
            llama_model, batch_size, sequence_length, hardware_name, format_type='pandas'
        )
        print(llama_roofline.to_string(index=False))
        print()
        
    except Exception as e:
        print(f"Error in dense model analysis: {e}")
        print()
    
    # 2. MoE Model Operator Analysis
    print("=== MoE Model (DeepSeek-style) Operator Analysis ===")
    try:
        # Create parallel configuration for MoE
        parallel_config = ParallelConfig(
            tensor_parallel_size=2,
            expert_parallel_size=4,
            expert_data_parallel_size=1
        )
        
        deepseek_table = generate_operator_table(
            deepseek_model, batch_size, sequence_length, hardware_name, 
            parallel_config, format_type='pandas'
        )
        print(deepseek_table.to_string(index=False))
        print()
        
        # Roofline analysis with parallel config
        print("=== MoE Model Roofline Analysis ===")
        deepseek_roofline = generate_roofline_table(
            deepseek_model, batch_size, sequence_length, hardware_name,
            parallel_config, format_type='pandas'
        )
        print(deepseek_roofline.to_string(index=False))
        print()
        
    except Exception as e:
        print(f"Error in MoE model analysis: {e}")
        print()
    
    # 3. Model Comparison
    print("=== Model Comparison ===")
    try:
        comparison_table = compare_models_table(
            [llama_model, deepseek_model],
            ['Llama-7B', 'DeepSeek-MoE'],
            batch_size, sequence_length, hardware_name,
            format_type='pandas'
        )
        print(comparison_table.to_string(index=False))
        print()
        
    except Exception as e:
        print(f"Error in model comparison: {e}")
        print()
    
    # 4. Paper-style LaTeX table
    print("=== Paper-style LaTeX Table (Dense Model) ===")
    try:
        latex_table = generate_paper_style_table(
            llama_model, batch_size, sequence_length, hardware_name
        )
        print(latex_table)
        print()
        
    except Exception as e:
        print(f"Error generating LaTeX table: {e}")
        print()
    
    # 5. Detailed operator breakdown
    print("=== Detailed Operator Breakdown (Dense Model) ===")
    try:
        breakdown = llama_model.get_operator_breakdown(batch_size, sequence_length, hardware_name)
        
        print(f"Hardware: {breakdown['hardware'].name}")
        print(f"Peak FP16 Performance: {breakdown['hardware'].peak_flops.get('fp16_tensor', 'N/A')} TFLOPS")
        print(f"Memory Bandwidth: {breakdown['hardware'].memory_bandwidth_gbps} GB/s")
        print()
        
        print("Per-operator details:")
        for op_name, op_data in breakdown['operators'].items():
            metrics = op_data['metrics_total']
            print(f"  {op_name}:")
            print(f"    FLOPs: {metrics.flops/1e12:.3f} TFLOPS")
            print(f"    Memory Capacity: {metrics.memory_capacity_bytes/(1024**3):.3f} GB")
            print(f"    Arithmetic Intensity: {metrics.arithmetic_intensity:.2f}")
            print(f"    Execution Time: {metrics.execution_time_ms:.2f} ms")
            print(f"    Utilization: {metrics.utilization:.1f}%")
            print()
        
        print("Model totals:")
        totals = breakdown['totals']
        print(f"  Total FLOPs: {totals['flops']/1e12:.3f} TFLOPS")
        print(f"  Total Memory Capacity: {totals['memory_capacity_bytes']/(1024**3):.3f} GB")
        print(f"  Total Time: {totals['execution_time_ms']:.2f} ms")
        print(f"  Throughput: {totals['throughput_tokens_per_sec']:.0f} tokens/s")
        print()
        
    except Exception as e:
        print(f"Error in detailed breakdown: {e}")
        print()
    
    print("=== Analysis Complete ===")


if __name__ == "__main__":
    main()