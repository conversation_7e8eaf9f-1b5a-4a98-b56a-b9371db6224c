from transformers import AutoConfig
import os
import json
import requests
from dotenv import load_dotenv
import time
from tabulate import tabulate
import pandas as pd
import sys

load_dotenv()


from configs import CACHE_DIR,MODELING_DIR, token,common_proxies

from utils import fetch_model_config, fetch_modeling_file



def compare_configs(model_configs):
    comparison = []
    shape_comparison = []
    params_to_compare = [
        ("max_position_embeddings", "最大位置嵌入数"),
        ("vocab_size", "词表大小"),
        ("num_hidden_layers", "层数"),
        ("hidden_size", "隐藏维度"),
        ("intermediate_size", "中间层维度"),
        ("num_attention_heads", "注意力头数"),
        # MOE
        ("n_routed_experts", "路由专家数"),
        ("n_shared_experts", "路由专家数"),
        ("first_k_dense_replace", "稠密层"),
        # MLA
        ("kv_lora_rank", "kv lora rank"),
        ("q_lora_rank", "q lora rank"),
        ("qk_nope_head_dim", "qk_nope_head_dim"),
        ("qk_rope_head_dim", "qk_rope_head_dim"),
        ("v_head_dim", "v_head_dim"),
        # MTP
        ("num_nextn_predict_layers", "num_nextn_predict_layers"),
    ]

    for model, config in model_configs.items():
        if not config:
            continue
        row = {"模型名称": model}
        for param, desc in params_to_compare:
            row[desc] = config.get(param, "N/A")
        comparison.append(row)

    def save_to_excel(df, filename="model_comparison.xlsx"):
        try:
            df.to_excel(filename, index=False)
            print(f"\033[32m成功\033[0m: 比较结果已保存到 {filename}")
        except Exception as e:
            print(f"\033[31m错误\033[0m: Excel文件保存失败 - {str(e)}")

    # 生成形状比较数据
    for model, config in model_configs.items():
        if not config:
            continue
        shape_row = {"模型名称": model}
        shape_row.update(calculate_operation_shapes(config))
        shape_comparison.append(shape_row)

    # 优化Excel写入逻辑
    try:
        # 检查文件是否存在
        file_exists = os.path.exists("model_comparison.xlsx")

        with pd.ExcelWriter(
            "model_comparison.xlsx", engine="openpyxl", mode="a" if file_exists else "w"
        ) as writer:
            pd.DataFrame(comparison).to_excel(
                writer, sheet_name="parameters", index=False
            )
            pd.DataFrame(shape_comparison).to_excel(
                writer, sheet_name="shapes", index=False
            )

            # 添加格式美化
            if writer.sheets["parameters"].max_row > 1:
                writer.sheets["parameters"].column_dimensions["A"].width = 40
                writer.sheets["shapes"].column_dimensions["A"].width = 40

            print("\033[32m成功\033[0m: 比较结果已保存到 model_comparison.xlsx")

    except Exception as e:
        print(f"\033[31m错误\033[0m: Excel文件保存失败 - {str(e)}")
        # 创建新文件
        with pd.ExcelWriter("model_comparison.xlsx", engine="openpyxl") as writer:
            pd.DataFrame(comparison).to_excel(
                writer, sheet_name="parameters", index=False
            )
            pd.DataFrame(shape_comparison).to_excel(
                writer, sheet_name="shapes", index=False
            )

    print(tabulate(comparison, headers="keys", tablefmt="github"))


def calculate_operation_shapes(config):
    model_type = config.get("model_type", "")
    shapes = {}

    # 通用参数
    hidden_size = config.get("hidden_size", 0)
    num_attention_heads = config.get("num_attention_heads", 1)
    num_key_value_heads = config.get("num_key_value_heads", num_attention_heads)

    # 通用投影层计算
    if model_type in ["llama"]:
        # 计算q_proj/k_proj维度（考虑分组注意力）
        q_dim = hidden_size * num_attention_heads // num_key_value_heads
        shapes["q_proj"] = f"({hidden_size} -> {q_dim})"
        shapes["k_proj"] = f"({hidden_size} -> {hidden_size // num_key_value_heads})"
    
    shapes["output_proj"] = (
            f"({hidden_size * num_attention_heads} -> {hidden_size})"
        )

    # 不同架构的特殊处理
    # Llama系列架构处理
    if model_type == "llama":
        # 补充o_proj计算
        shapes["o_proj"] = f"({hidden_size} -> {hidden_size * num_attention_heads})"

        if "intermediate_size" in config:
            # FFN层维度
            shapes.update(
                {
                    "gate_proj": f"({hidden_size} -> {config['intermediate_size']})",
                    "up_proj": f"({hidden_size} -> {config['intermediate_size']})",
                    "down_proj": f"({config['intermediate_size']} -> {hidden_size})",
                }
            )
    # Deepseek系列架构处理
    elif model_type in ["deepseek_v3"]:
        # KV投影层特殊处理
        kv_lora_rank = config.get("kv_lora_rank", 0)
        qk_rope_head_dim = config.get("qk_rope_head_dim", 0)
        v_head_dim = config.get("v_head_dim", 0)
        q_head_dim = config.qk_nope_head_dim + config.qk_rope_head_dim

        if config.q_lora_rank is None:
            shapes["q_proj"] = f"({hidden_size} -> {config.num_heads * q_head_dim})"
        else:
            shapes["q_a_proj"] = f"({hidden_size} -> {config.q_lora_rank})"
            shapes["q_b_proj"] = f"({config.q_lora_rank} -> {config.num_heads * q_head_dim})"

        shapes["kv_a_proj"] = f"({hidden_size} -> {kv_lora_rank + qk_rope_head_dim})"
        shapes["kv_b_proj"] = (
            f"({kv_lora_rank} -> {num_attention_heads * (q_head_dim - qk_rope_head_dim + v_head_dim)})"
        )

        if "n_routed_experts" in config:
            expert_dim = config.get("moe_intermediate_size", hidden_size * 4)
            shapes.update(
                {
                    "gate_proj": f"({hidden_size} -> {config['n_routed_experts'] * 8})",
                    "expert_proj": f"({hidden_size} -> {expert_dim})",
                }
            )
            
    return shapes


if __name__ == "__main__":
    

    
    os.makedirs(CACHE_DIR, exist_ok=True)
    
    os.makedirs(MODELING_DIR, exist_ok=True)

    models = sys.argv[1:] or [
        "meta-llama/Llama-3.2-3B-Instruct",
        "meta-llama/Llama-3.2-1B-Instruct",
        "deepseek-ai/DeepSeek-v2-lite",
        "deepseek-ai/DeepSeek-R1",
    ]
    configs = {model: fetch_model_config(model) for model in models}
    modeling_files = {model: fetch_modeling_file(model) for model in models}
    compare_configs(configs)
