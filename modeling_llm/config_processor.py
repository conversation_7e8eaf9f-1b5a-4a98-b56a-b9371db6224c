import pandas as pd
import json
import os
from fetch import fetch_model_config
from interests_model import models


def compare_configs():
    try:
        with open("map.json") as f:
            mapping = json.load(f)
    except FileNotFoundError:
        print("\033[31m错误\033[0m: 找不到映射文件map.json，请先运行fetch.py")
        return

    comparison = []
    params_to_compare = [
        ("max_position_embeddings", "最大位置嵌入数"),
        ("vocab_size", "词表大小"),
        ("num_hidden_layers", "层数"),
        ("hidden_size", "隐藏维度"),
        ("intermediate_size", "中间层维度"),
        ("num_attention_heads", "注意力头数"),
    ]

    for model in models:
        config_path = mapping.get(model, {}).get("config")
        if not config_path or not os.path.exists(config_path):
            print(f"\033[33m警告\033[0m: {model} 配置缺失，正在尝试重新下载...")
            fetch_model_config(model)

        with open(config_path) as f:
            config = json.load(f)

        row = {"模型名称": model}
        for param, desc in params_to_compare:
            row[desc] = config.get(param, "N/A")
        comparison.append(row)

    save_to_excel(pd.DataFrame(comparison), "parameters")


def save_to_excel(df, sheet_name):
    try:
        with pd.ExcelWriter(
            "model_comparison.xlsx",
            engine="openpyxl",
            mode="a" if os.path.exists("model_comparison.xlsx") else "w",
        ) as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)

            if writer.sheets[sheet_name].max_row > 1:
                writer.sheets[sheet_name].column_dimensions["A"].width = 40

            print(f"\033[32m成功\033[0m: {sheet_name}对比已保存到Excel")
    except Exception as e:
        print(f"\033[31m错误\033[0m: Excel操作失败 - {str(e)}")


if __name__ == "__main__":
    compare_configs()
