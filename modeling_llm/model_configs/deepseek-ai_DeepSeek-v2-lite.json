{"vocab_size": 102400, "max_position_embeddings": 163840, "hidden_size": 2048, "intermediate_size": 10944, "moe_intermediate_size": 1408, "num_hidden_layers": 27, "num_attention_heads": 16, "n_shared_experts": 2, "n_routed_experts": 64, "ep_size": 1, "routed_scaling_factor": 1.0, "kv_lora_rank": 512, "q_lora_rank": null, "qk_rope_head_dim": 64, "v_head_dim": 128, "qk_nope_head_dim": 128, "topk_method": "greedy", "n_group": 1, "topk_group": 1, "num_experts_per_tok": 6, "moe_layer_freq": 1, "first_k_dense_replace": 1, "norm_topk_prob": false, "scoring_func": "softmax", "aux_loss_alpha": 0.001, "seq_aux": true, "num_key_value_heads": 16, "hidden_act": "silu", "initializer_range": 0.02, "rms_norm_eps": 1e-06, "pretraining_tp": 1, "use_cache": true, "rope_theta": 10000, "rope_scaling": {"beta_fast": 32, "beta_slow": 1, "factor": 40, "mscale": 0.707, "mscale_all_dim": 0.707, "original_max_position_embeddings": 4096, "type": "yarn"}, "attention_bias": false, "attention_dropout": 0.0, "return_dict": true, "output_hidden_states": false, "output_attentions": false, "torchscript": false, "torch_dtype": "bfloat16", "use_bfloat16": false, "tf_legacy_loss": false, "pruned_heads": {}, "tie_word_embeddings": false, "chunk_size_feed_forward": 0, "is_encoder_decoder": false, "is_decoder": false, "cross_attention_hidden_size": null, "add_cross_attention": false, "tie_encoder_decoder": false, "max_length": 20, "min_length": 0, "do_sample": false, "early_stopping": false, "num_beams": 1, "num_beam_groups": 1, "diversity_penalty": 0.0, "temperature": 1.0, "top_k": 50, "top_p": 1.0, "typical_p": 1.0, "repetition_penalty": 1.0, "length_penalty": 1.0, "no_repeat_ngram_size": 0, "encoder_no_repeat_ngram_size": 0, "bad_words_ids": null, "num_return_sequences": 1, "output_scores": false, "return_dict_in_generate": false, "forced_bos_token_id": null, "forced_eos_token_id": null, "remove_invalid_values": false, "exponential_decay_length_penalty": null, "suppress_tokens": null, "begin_suppress_tokens": null, "architectures": ["DeepseekV2ForCausalLM"], "finetuning_task": null, "id2label": {"0": "LABEL_0", "1": "LABEL_1"}, "label2id": {"LABEL_0": 0, "LABEL_1": 1}, "tokenizer_class": null, "prefix": null, "bos_token_id": 100000, "pad_token_id": null, "eos_token_id": 100001, "sep_token_id": null, "decoder_start_token_id": null, "task_specific_params": null, "problem_type": null, "_name_or_path": "deepseek-ai/DeepSeek-v2-lite", "_attn_implementation_autoset": false, "transformers_version": "4.49.0", "auto_map": {"AutoConfig": "deepseek-ai/DeepSeek-v2-lite--configuration_deepseek.DeepseekV2Config", "AutoModel": "deepseek-ai/DeepSeek-v2-lite--modeling_deepseek.DeepseekV2Model", "AutoModelForCausalLM": "deepseek-ai/DeepSeek-v2-lite--modeling_deepseek.DeepseekV2ForCausalLM"}, "model_type": "deepseek_v2"}