{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\Lib\\site-packages\\tqdm\\auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "None of PyTorch, TensorFlow >= 2.0, or Flax have been found. Models won't be available and only tokenizers, configuration and file/data utilities can be used.\n"]}], "source": ["from transformers import AutoConfig\n", "\n", "from utils import token, common_proxies\n", "import numpy as np\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["config =AutoConfig.from_pretrained(\n", "                \"deepseek-ai/DeepSeek-R1\",\n", "                request_timeout=5,\n", "                num_retries=2,\n", "                token=token,\n", "                trust_remote_code=True,\n", "                proxies=common_proxies,\n", "            ) "]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def get_prod(shapes):\n", "    return np.sum([np.prod(v) for k, v in shapes.items()])\n", "\n", "\n", "class BaseModel:\n", "    def __init__(self, model_name, config=None):\n", "        self.model_name = model_name\n", "        if config is not None:\n", "            self.config = config\n", "        else:\n", "            self.config = AutoConfig.from_pretrained(\n", "                model_name,\n", "                request_timeout=5,\n", "                num_retries=2,\n", "                token=token,\n", "                trust_remote_code=True,\n", "                proxies=common_proxies,\n", "            )\n", "        self.config_dict = self.config.to_dict()\n", "\n", "        self.hidden_size = self.config.hidden_size\n", "        self.intermediate_size = self.config.intermediate_size\n", "        self.num_hidden_layers = self.config.num_hidden_layers\n", "        self.head_dim = getattr(\n", "            self.config,\n", "            \"head_dim\",\n", "            self.config.hidden_size // self.config.num_attention_heads,\n", "        )\n", "\n", "    def get_layer_attention_param(self):\n", "        self.att_matmuls = {\n", "            \"q_proj\": [\n", "                self.hidden_size,\n", "                self.config.num_attention_heads * self.head_dim,\n", "            ],\n", "            \"k_proj\": [\n", "                self.hidden_size,\n", "                self.config.num_key_value_heads * self.head_dim,\n", "            ],\n", "            \"v_proj\": [\n", "                self.hidden_size,\n", "                self.config.num_key_value_heads * self.head_dim,\n", "            ],\n", "            \"o_proj\": [\n", "                self.config.num_attention_heads * self.head_dim,\n", "                self.hidden_size,\n", "            ],\n", "        }\n", "        ops = get_prod(self.att_matmuls)\n", "        return ops\n", "\n", "    def get_layer_mlp_param(self):\n", "        self.mlp_matmuls = {\n", "            \"gate_proj\": [self.hidden_size, self.intermediate_size],\n", "            \"up_proj\": [self.hidden_size, self.intermediate_size],\n", "            \"down_proj\": [self.intermediate_size, self.hidden_size],\n", "        }\n", "        ops = get_prod(self.mlp_matmuls)\n", "        return ops\n", "\n", "    def get_emb_head_param(self):\n", "        return self.config.vocab_size * self.hidden_size * 2\n", "\n", "    def get_param(self):\n", "        ops = self.num_hidden_layers * self.get_layer_attention_param()\n", "        ops += (\n", "            self.num_hidden_layers * self.get_layer_mlp_param()\n", "            + self.get_emb_head_param()\n", "        )\n", "        return ops\n", "\n", "\n", "class DeepseekModel(BaseModel):\n", "    def __init__(self, model_name, config=None):\n", "        super().__init__(model_name, config)\n", "        for key in [\n", "            \"num_experts_per_tok\",\n", "            \"n_shared_experts\",\n", "            \"n_routed_experts\",\n", "            \"q_lora_rank\",\n", "            \"kv_lora_rank\",\n", "            \"qk_rope_head_dim\",\n", "            \"v_head_dim\",\n", "            \"qk_nope_head_dim\",\n", "            \"moe_intermediate_size\",\n", "            \"first_k_dense_replace\",\n", "        ]:\n", "            setattr(self, key, self.config_dict.get(key, None))\n", "        self.q_head_dim = self.config.qk_nope_head_dim + self.config.qk_rope_head_dim\n", "        self.num_heads = self.config.num_attention_heads\n", "\n", "    def get_layer_attention_param(self):\n", "        ops = 0\n", "        if self.q_lora_rank is not None:\n", "            self.att_matmuls = {\n", "                \"q_a_proj\": [self.hidden_size, self.q_lora_rank],\n", "                \"q_b_proj\": [self.q_lora_rank, self.num_heads * self.q_head_dim],\n", "                \"kv_a_proj_with_mqa\": [\n", "                    self.hidden_size,\n", "                    (self.kv_lora_rank + self.qk_rope_head_dim),\n", "                ],\n", "                \"kv_b_proj\": [\n", "                    self.kv_lora_rank,\n", "                    self.num_heads\n", "                    * (self.q_head_dim - self.qk_rope_head_dim + self.v_head_dim),\n", "                ],\n", "                \"o_proj\": [self.num_heads * self.v_head_dim, self.hidden_size],\n", "            }\n", "            ops += np.sum([np.prod(v) for k, v in self.att_matmuls.items()])\n", "            return ops\n", "\n", "    def get_layer_moe_param_full(self):\n", "        self.expert_matmuls = {\n", "            \"gate_proj\": [self.hidden_size, self.moe_intermediate_size],\n", "            \"up_proj\": [self.hidden_size, self.moe_intermediate_size],\n", "            \"down_proj\": [self.moe_intermediate_size, self.hidden_size],\n", "        }\n", "        ops = (self.n_routed_experts + self.n_shared_experts) * get_prod(\n", "            self.expert_matmuls\n", "        ) + self.n_routed_experts * self.hidden_size  # gating\n", "        return ops\n", "\n", "    def get_layer_moe_param_per_token(self):\n", "        self.expert_matmuls = {\n", "            \"gate_proj\": [self.hidden_size, self.moe_intermediate_size],\n", "            \"up_proj\": [self.hidden_size, self.moe_intermediate_size],\n", "            \"down_proj\": [self.moe_intermediate_size, self.hidden_size],\n", "        }\n", "        ops = (self.num_experts_per_tok + self.n_shared_experts) * get_prod(\n", "            self.expert_matmuls\n", "        ) + self.n_routed_experts * self.hidden_size  # gating\n", "        return ops\n", "\n", "    def get_param(self, full=False):\n", "        ops = self.num_hidden_layers * self.get_layer_attention_param()\n", "        ops += (self.num_hidden_layers - self.first_k_dense_replace) * (\n", "            self.get_layer_moe_param_full()\n", "            if full\n", "            else self.get_layer_moe_param_per_token()\n", "        )\n", "        ops += (\n", "            self.first_k_dense_replace * self.get_layer_mlp_param()\n", "            + self.get_emb_head_param()\n", "        )\n", "        return ops\n", "\n", "    def print(self):\n", "        print(f\"{self.att_matmuls=}\")\n", "        print(f\"{self.expert_matmuls=}\")\n", "        print(f\"{self.mlp_matmuls=}\")\n", "\n", "\n", "# m = BaseModel(\"meta-llama/Llama-3.2-3B-Instruct\")\n", "# m.get_param()\n", "\n", "m = DeepseekModel(\"deepseek-ai/DeepSeek-R1\", config)\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(671.02539776)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["m.get_param() / 1e9\n", "m.get_param(True) / 1e9\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Accelerator:\n", "    def __init__(self, name, **config):\n", "        self.name = name\n", "        self.config = config\n", "        attrs = [\n", "            \"fp16_ops\",\n", "            \"bf16_ops\",\n", "            \"int8_ops\",\n", "            \"mem_size\",\n", "            \"mem_bandwidth\",\n", "            \"l2_size\",\n", "            \"l2_bandwidth\",\n", "            \"comm_bandwidth\",\n", "        ]\n", "        for attr in attrs:\n", "            setattr(self, attr, config.get(attr, 0))\n", "\n", "    def __str__(self):\n", "        pass\n", "\n", "\n", "hardwares = [\n", "    {\n", "        \"name\": \"h20\", \n", "        \"fp16_ops\",\n", "            \"bf16_ops\",\n", "            \"int8_ops\",\n", "            \"mem_size\",\n", "            'mem_bandwidth',\n", "            'l2_size',\n", "            'l2_bandwidth',\n", "\n", "            'comm_bandwidth'\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 2}