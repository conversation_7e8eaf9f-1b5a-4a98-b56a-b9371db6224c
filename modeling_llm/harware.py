h100_sxm5_specs = {
    "architecture": "Hopper",
    "fp32_cudacore_tops": 60,
    "fp16_cudacore_tops": 120,
    "fp16_bf16_tensorcore_tops": 1000,
    "int8_tensorcore_tops": 2000,
    "fp8_tensorcore_tops": 2000,
    "mem_type": "HBM3",
    "mem_size_gb": 80,
    "mem_bandwidth_tb": 3,
    "l2_size_mb": 50,
    "l2_bandwidth": "Not directly specified",
    "comm_bandwidth_gb": 900,
    "l1_size_kb": 256,
}

h100_pcie_specs = {
    "architecture": "Hopper",
    "fp32_cudacore_tops": 48,
    "fp16_cudacore_tops": 96,
    "fp16_bf16_tensorcore_tops": 800,
    "int8_tensorcore_tops": 1600,
    "fp8_tensorcore_tops": 1600,
    "mem_type": "HBM2e",
    "mem_size_gb": 80,
    "mem_bandwidth_tb": 2,
    "l2_size_mb": 50,
    "l2_bandwidth": "Not directly specified",
    "comm_bandwidth_gb": 900,
    "l1_size_kb": 256,
}

# NVIDIA B200 specs
b200_specs = {
    "architecture": "Blackwell",
    "fp64_cudacore_tops": 40,
    "fp64_tensorcore_tops": 40,
    "fp32_cudacore_tops": 80,
    "fp32_tensorcore_tops": 2200,
    "fp16_bf16_tensorcore_tops": 4500,
    "int8_tensorcore_tops": 9000,
    "fp8_tensorcore_tops": 9000,
    "fp4_tensorcore_tops": 18000,
    "gpu_memory": 192,
    "mem_bandwidth_tb": 8,
    "comm_bandwidth_gb": 1800,
}

# NVIDIA B100 specs
b100_specs = {
    "architecture": "Blackwell",
    "fp64_cudacore_tops": 30,
    "fp64_tensorcore_tops": 30,
    "fp32_cudacore_tops": 60,
    "fp32_tensorcore_tops": 1800,
    "fp16_bf16_tensorcore_tops": 3500,
    "int8_tensorcore_tops": 7000,
    "fp8_tensorcore_tops": 7000,
    "fp4_tensorcore_tops": 14000,
    "gpu_memory": 192,
    "mem_bandwidth_tb": 8,
    "comm_bandwidth_gb": 1800,
}

# NVIDIA H200 specs
h200_specs = {
    "architecture": "Hopper",
    "fp64_cudacore_tops": 34,
    "fp64_tensorcore_tops": 67,
    "fp32_cudacore_tops": 67,
    "fp32_tensorcore_tops": 989,
    "fp16_bf16_tensorcore_tops": 1979,
    "int8_tensorcore_tops": 3958,
    "fp8_tensorcore_tops": 3958,
    "gpu_memory": 141,
    "mem_bandwidth_tb": 4.8,
    "comm_bandwidth_gb": 900,
}

# NVIDIA A100 specs
# a100_specs = {
#     "architecture": "Ampere",
#     "fp64_cudacore_tops": 9.7,
#     "fp64_tensorcore_tops": 19.5,
#     "fp32_cudacore_tops": 19.5,
#     "fp32_tensorcore_tops": 312,
#     "fp16_bf16_tensorcore_tops": 624,
#     "int8_tensorcore_tops": 1248,
#     "gpu_memory": 80,
#     "mem_bandwidth_tb": 2,
#     "comm_bandwidth_gb": 600,
# }

a100_gpu_params = {
    "fp64_cudacore_tops": 9.7,
    "fp64_tensorcore_tops": 19.5,
    "fp32_cudacore_tops": 19.5,
    "fp16_cudacore_tops": 78,
    "bf16_cudacore_tops": 39,
    "fp16_bf16_tensorcore_tops": 312,
    "int8_tensorcore_tops": 624,
    "int4_tensorcore_tops": 1248,
    "mem_type": "HBM2",
    "mem_size_gb": 40,
    "mem_bandwidth_tb": 1.555,
    "l2_size_mb": 40,
    "l2_bandwidth": "Not directly specified",
    "comm_bandwidth_gb": 600,
    "l1_size_kb": 192
}