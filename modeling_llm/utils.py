from transformers import AutoConfig
import os
import json
import requests
from dotenv import load_dotenv
import time
from tabulate import tabulate
import pandas as pd

from configs import CACHE_DIR,MODELING_DIR, token,common_proxies

def fetch_modeling_file(model_name, repo_type="model"):
    # 从配置文件获取架构名称
    config_path = os.path.join(CACHE_DIR, f"{model_name.replace('/', '_')}.json")
    with open(config_path, 'r') as f:
        config = json.load(f)
    arch_name = config['architectures'][0].replace('ForCausalLM', '').lower()
    
    filename = f"modeling_{arch_name}.py"
    
    try:
        model_type = model_name.split("/")[-1].lower().split('-')[0]

        fetch_filename = f"modeling_{model_type}.py"

        cache_path = os.path.join(MODELING_DIR, filename)

        if os.path.exists(cache_path):
            cache_age = (time.time() - os.path.getmtime(cache_path)) / 86400
            if cache_age < int(os.getenv("CACHE_EXPIRY_DAYS", 30)):
                return cache_path
            os.remove(cache_path)

        url = f"https://huggingface.co/{model_name}/resolve/main/{fetch_filename}"
        response = requests.get(
            url,
            proxies=common_proxies,
            headers={"Authorization": f"Bearer {token}"},
            timeout=10,
            stream=True,
        )
        response.raise_for_status()

        with open(cache_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        print(f"\033[32m成功\033[0m: 建模文件已缓存到 {cache_path}")
        return cache_path
    except Exception as e:
        print(f"\033[31mError\033[0m: 无法下载建模文件 - {str(e)}")
        return None


def fetch_model_config(model_name):
    cache_path = os.path.join(CACHE_DIR, f"{model_name.replace('/', '_')}.json")

    if os.path.exists(cache_path):
        # 添加缓存有效期检查（默认30天）
        cache_expiry_days = int(os.getenv("CACHE_EXPIRY_DAYS", 30))
        file_age = (time.time() - os.path.getmtime(cache_path)) / 86400

        if file_age < cache_expiry_days:
            with open(cache_path, "r") as f:
                return json.load(f)
        print(
            f"\033[33mWarning\033[0m: 本地缓存已过期（{file_age:.1f}天），将尝试重新下载配置"
        )
        os.remove(cache_path)

    try:
        config = AutoConfig.from_pretrained(
            model_name,
            request_timeout=5,
            num_retries=2,
            token=token,
            trust_remote_code=True,
            proxies=common_proxies,
        )
        config_dict = config.to_dict()

        with open(cache_path, "w") as f:
            json.dump(config_dict, f, indent=2)

        return config_dict
    except Exception as e:
        print(f"\033[31mError\033[0m: 无法下载 {model_name} 的配置")
        print(f"可能原因: {str(e)}")
        if "CERTIFICATE_VERIFY_FAILED" in str(e):
            print(
                "\033[33m提示\033[0m: 如果使用代理，请检查代理证书设置，可尝试在.env文件中添加:\nREQUESTS_CA_BUNDLE=/path/to/certificate.pem"
            )
        elif "ConnectionError" in str(e):
            print(
                "\033[33m提示\033[0m: 网络连接失败，请检查代理设置。可通过以下方式配置代理:\n1. 在.env文件中添加 HTTP_PROXY=http://127.0.0.1:7890\n2. 或在命令前设置临时变量：set HTTP_PROXY=http://127.0.0.1:7890 && python ..."
            )
        print(f"本地缓存路径: {cache_path}")
        if os.path.exists(cache_path):
            print("正在使用最后一次成功的缓存配置")
            with open(cache_path, "r") as f:
                return json.load(f)
        return None

