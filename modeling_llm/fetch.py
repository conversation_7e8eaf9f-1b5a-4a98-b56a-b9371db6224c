import os
import json
import requests
from dotenv import load_dotenv
import time
from configs import CACHE_DIR, MODELING_DIR, token, common_proxies

from transformers import AutoConfig
import re

load_dotenv()

def fetch_model_config(model_name):
    cache_path = os.path.join(CACHE_DIR, f"{model_name.replace('/', '_')}.json")
    
    if os.path.exists(cache_path):
        cache_expiry_days = int(os.getenv("CACHE_EXPIRY_DAYS", 30))
        file_age = (time.time() - os.path.getmtime(cache_path)) / 86400
        if file_age < cache_expiry_days:
            with open(cache_path, "r") as f:
                return json.load(f)
        os.remove(cache_path)

    try:
        config = AutoConfig.from_pretrained(
            model_name,
            request_timeout=5,
            num_retries=2,
            token=token,
            trust_remote_code=True,
            proxies=common_proxies,
        )
        config_dict = config.to_dict()
        with open(cache_path, "w") as f:
            json.dump(config_dict, f, indent=2)
        return config_dict
    except Exception as e:
        print(f"获取配置失败: {str(e)}")
        return None

def fetch_modeling_file(model_name):
    try:
        config = fetch_model_config(model_name)
        arch_name = re.sub(r'v?\d+', '', config['architectures'][0].replace('ForCausalLM', ''), flags=re.IGNORECASE).lower()
        filename = f"modeling_{arch_name}.py"
        cache_path = os.path.join(MODELING_DIR, filename)

        if os.path.exists(cache_path):
            return cache_path

        response = requests.get(
            f"https://huggingface.co/{model_name}/resolve/main/{filename}",
            proxies=common_proxies,
            headers={"Authorization": f"Bearer {token}"},
            stream=True
        )
        response.raise_for_status()

        with open(cache_path, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        # 生成映射关系
        update_mapping(model_name, {
            "config": cache_path.replace(os.getcwd(), '.'),
            "modeling": cache_path.replace(os.getcwd(), '.')
        })

        return cache_path
    except Exception as e:
        print(f"下载建模文件失败: {str(e)}")
        return None

def update_mapping(model_name, paths):
    mapping_file = "map.json"
    mapping = {}
    if os.path.exists(mapping_file):
        with open(mapping_file, 'r') as f:
            mapping = json.load(f)
    mapping[model_name] = paths
    with open(mapping_file, 'w') as f:
        json.dump(mapping, f, indent=2)

if __name__ == "__main__":
    import sys
    
    try:
        os.makedirs(CACHE_DIR, exist_ok=True)
        os.makedirs(MODELING_DIR, exist_ok=True)
    except Exception as e:
        print(f"\033[31m错误\033[0m: 目录创建失败 - {str(e)}")
        sys.exit(1)

    models = sys.argv[1:] or [
        "meta-llama/Llama-3.2-3B-Instruct",
        "meta-llama/Llama-3.2-1B-Instruct",
        "deepseek-ai/DeepSeek-v2-lite",
        "deepseek-ai/DeepSeek-R1",
    ]

    if not models:
        print("使用方法: python fetch.py [模型名称1] [模型名称2]...")
        print("示例:\n  python fetch.py meta-llama/Llama-3.2-3B-Instruct deepseek-ai/DeepSeek-R1")
        sys.exit(1)

    print(f"正在获取{len(models)}个模型的配置和建模文件...")
    for model in models:
        print(f"\n=== 处理模型: {model} ===")
        config = fetch_model_config(model)
        if config:
            print(f"✅ 配置获取成功")
            modeling_file = fetch_modeling_file(model)
            if modeling_file:
                print(f"✅ 建模文件已保存到: {modeling_file}")
        else:
            print(f"❌ 无法获取模型配置")

    print("\n所有模型处理完成，映射关系已更新到map.json")