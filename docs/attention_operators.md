# Attention Operators Documentation

This document describes the attention operator architecture in the LLM Modeling Metrics library, designed to support various attention mechanisms including MHA, GQA, and MLA.

## Overview

The attention operator system provides a modular way to model different attention mechanisms with accurate FLOP and memory calculations. Each operator inherits from `BaseOperator` and implements mechanism-specific computations.

## Supported Attention Mechanisms

### 1. Multi-Head Attention (MHA) - `AttentionOperator`

Standard transformer attention with separate query, key, and value heads.

```python
from llm_modeling_metrics.core.operators import AttentionOperator

# Create MHA operator
mha = AttentionOperator(
    hidden_size=4096,
    num_heads=32,
    num_kv_heads=32,  # Same as num_heads for MHA
    precision='fp16'
)

# Compute FLOPs for decode phase
flops = mha.compute_flops(batch_size=1, qlen=1, kv_lens=2048)
```

**Use cases:**
- Small to medium models where memory is not a constraint
- Research and experimentation
- When maximum attention quality is needed

### 2. Grouped Query Attention (GQA) - `AttentionOperator`

Reduces memory usage by sharing key-value heads across multiple query heads.

```python
# Create GQA operator with 8 KV heads
gqa = AttentionOperator(
    hidden_size=4096,
    num_heads=32,
    num_kv_heads=8,  # Fewer KV heads for memory efficiency
    precision='fp16'
)
```

**Use cases:**
- Good balance between performance and memory efficiency
- Medium to large models
- Production deployments with memory constraints

### 3. Multi-head Latent Attention (MLA) - `MLAAttentionOperator`

DeepSeek V3's attention mechanism with compressed KV representations using LoRA-style projections. All linear operations use `MatMulOperator` for consistency and detailed analysis.

```python
from llm_modeling_metrics.core.operators import MLAAttentionOperator

# Create MLA operator
mla = MLAAttentionOperator(
    hidden_size=7168,
    num_heads=128,
    kv_lora_rank=512,      # Compressed KV representation
    q_lora_rank=1536,      # Query LoRA rank
    qk_rope_head_dim=64,   # RoPE dimension
    qk_nope_head_dim=128,  # Non-RoPE dimension
    v_head_dim=128,        # Value head dimension
    precision='fp16'
)

# Separate linear projections from attention computation
linear_flops = mla.compute_linear_projection_flops(batch_size=1, qlen=1)
attention_flops = mla.compute_attention_computation_flops(batch_size=1, qlen=1, kv_lens=8192)

# Detailed breakdown of individual projections
projection_breakdown = mla.get_projection_breakdown(batch_size=1, qlen=1)
for name, info in projection_breakdown.items():
    print(f"{name}: {info['flops']/1e6:.2f} MFLOPs, {info['weight_params']/1e6:.2f}M params")
```

**Use cases:**
- Very large models (100B+ parameters)
- Long context scenarios
- Memory-critical deployments
- When KV cache size is the bottleneck

## Key Features

### Modular Design

Each attention mechanism is implemented as a separate operator class, making it easy to:
- Compare different mechanisms
- Extend with new attention types
- Integrate into model analysis pipelines

### Accurate Calculations

All operators provide:
- **FLOP counting**: Precise floating-point operation counts
- **Memory capacity**: Peak memory requirements
- **Memory movement**: Data transfer requirements

### Flexible Analysis

The MLA operator provides additional methods for detailed analysis:
- `compute_linear_projection_flops()`: FLOPs for QKV projections and output projection
- `compute_attention_computation_flops()`: FLOPs for attention computation only
- `get_projection_breakdown()`: Detailed analysis of each MatMul operation
- `get_projection_operators()`: Access to individual MatMul operators
- Supports Table 2 style breakdowns from research papers

### MatMul Operator Integration

All linear operations in `MLAAttentionOperator` use `MatMulOperator` internally:
- **q_compress**: Input compression for queries
- **q_decompress**: Query decompression to attention heads
- **kv_compress**: KV compression to latent space
- **kv_decompress**: KV decompression from latent space
- **o_proj**: Output projection back to hidden dimension

This provides consistent FLOP calculations and detailed memory analysis.

## Performance Comparison

Based on a 4096D hidden size, 32 heads model:

| Mechanism | FLOPs (GFLOPs) | Memory (MB) | Key Advantage |
|-----------|----------------|-------------|---------------|
| MHA       | 0.084          | 167.97      | Maximum quality |
| GQA (8 KV)| 0.059          | 92.46       | Balanced efficiency |
| MLA       | 0.043          | 1.18        | Minimal memory |

## Extension Guide

To add a new attention mechanism:

1. **Inherit from BaseOperator**:
```python
from llm_modeling_metrics.core.operators import BaseOperator

class MyAttentionOperator(BaseOperator):
    def __init__(self, hidden_size, num_heads, **kwargs):
        super().__init__("MyAttention", precision)
        # Initialize mechanism-specific parameters
```

2. **Implement required methods**:
```python
def compute_flops(self, batch_size=1, qlen=1, kv_lens=2048):
    # Calculate FLOPs for your attention mechanism
    return total_flops

def compute_memory_capacity_bytes(self, batch_size=1, qlen=1, kv_lens=2048):
    # Calculate peak memory requirements
    return total_memory_bytes

def compute_memory_movement_bytes(self, batch_size=1, qlen=1, kv_lens=2048):
    # Calculate data movement requirements
    return total_movement_bytes
```

3. **Add mechanism-specific methods** (optional):
```python
def compute_custom_metric(self, **kwargs):
    # Add any mechanism-specific analysis methods
    return custom_result
```

## Future Extensions

Potential attention mechanisms to implement:

### FlashAttention
- Memory-efficient attention with tiling
- Reduced memory complexity from O(N²) to O(N)
- Important for very long sequences

### PagedAttention
- Dynamic memory allocation for KV cache
- Efficient memory usage in serving scenarios
- Non-contiguous memory layout support

### Sliding Window Attention
- Local attention patterns
- Constant memory complexity
- Good for streaming applications

### Sparse Attention
- Attention with sparse patterns (e.g., Longformer, BigBird)
- Reduced computational complexity
- Suitable for document-level tasks

## Integration Examples

### Model Analysis
```python
# Compare attention mechanisms in a model
from llm_modeling_metrics.models.dense_model import DenseModel

model = DenseModel("my-model", config)

# Analyze with different attention types
mha_flops = model.compute_flops_with_attention(AttentionOperator(...))
mla_flops = model.compute_flops_with_attention(MLAAttentionOperator(...))
```

### Research Analysis
```python
# Reproduce paper results
mla = MLAAttentionOperator(...)

# Table 2 style breakdown
linear_flops = mla.compute_linear_projection_flops(1, 1)
attention_flops = mla.compute_attention_computation_flops(1, 1, 8192)

print(f"Linear Projections: {linear_flops:.2e} FLOPs")
print(f"Attention Computation: {attention_flops:.2e} FLOPs")
```

### Production Planning
```python
# Memory planning for deployment
operators = [
    AttentionOperator(hidden_size, num_heads, num_heads),  # MHA
    AttentionOperator(hidden_size, num_heads, 8),          # GQA
    MLAAttentionOperator(hidden_size, num_heads, ...)      # MLA
]

for op in operators:
    memory_gb = op.compute_memory_capacity_bytes(1, 1, context_len) / 1e9
    print(f"{op.name}: {memory_gb:.2f} GB")
```

## Best Practices

1. **Choose the right mechanism**:
   - MHA: Small models, research
   - GQA: Production balance
   - MLA: Large models, memory-critical

2. **Consider context length**:
   - Memory scales quadratically with sequence length for MHA/GQA
   - MLA provides better scaling for long contexts

3. **Precision selection**:
   - fp16/bf16 for production efficiency
   - fp32 for research accuracy

4. **Batch size effects**:
   - Memory scales linearly with batch size
   - Consider memory constraints in serving

5. **Hardware considerations**:
   - GPU memory bandwidth affects performance
   - Consider arithmetic intensity for different mechanisms